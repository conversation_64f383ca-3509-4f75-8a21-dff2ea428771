#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PublishPress Authors\n"
"POT-Creation-Date: 2025-07-23 09:55+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON> <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: publishpress-authors.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: assets_wp\n"
"X-Poedit-SearchPathExcluded-1: dist\n"
"X-Poedit-SearchPathExcluded-2: legacy-tests\n"
"X-Poedit-SearchPathExcluded-3: tests\n"
"X-Poedit-SearchPathExcluded-4: vendor\n"
"X-Poedit-SearchPathExcluded-5: *.min.js\n"
"X-Poedit-SearchPathExcluded-6: src/assets/lib\n"
"Language: "

#: src/functions/template-tags.php:982 src/functions/template-tags.php:1603
msgid " and "
msgstr ""

#: src/core/Classes/Post_Editor.php:179
msgid "\"post_author\" is empty"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:419
#: src/modules/author-custom-fields/author-custom-fields.php:183
#, php-format
msgid "%1$s Image"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:878
#, php-format
msgid "%1s After Display Suffix"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1014
#, php-format
msgid "%1s Alignment"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:870
#, php-format
msgid "%1s Before Display Suffix"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1030
#, php-format
msgid "%1s Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:999
#, php-format
msgid "%1s Decoration"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:836
#, php-format
msgid "%1s Display"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:890
#, php-format
msgid "%1s Display Icon"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:909
#, php-format
msgid "%1s Display Icon Background Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:917
#, php-format
msgid "%1s Display Icon Border Radius (%2s)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:900
#, php-format
msgid "%1s Display Icon Size"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:854
#, php-format
msgid "%1s Display Prefix"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:862
#, php-format
msgid "%1s Display Suffix"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:805
#, php-format
msgid "%1s HTML Tag"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:940
#, php-format
msgid "%1s Line Height (px)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:929
#, php-format
msgid "%1s Size"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:985
#, php-format
msgid "%1s Style"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:970
#, php-format
msgid "%1s Transform"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:826
#, php-format
msgid "%1s Value Prefix"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:948
#, php-format
msgid "%1s Weight"
msgstr ""

#: src/functions/template-tags.php:733
#, php-format
msgid "%1sView all posts%2s by %3s"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:412
#: src/modules/author-custom-fields/author-custom-fields.php:176
#, php-format
msgid "%2$s"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:425
#: src/modules/author-custom-fields/author-custom-fields.php:189
#, php-format
msgid "%2$s list"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:424
#: src/modules/author-custom-fields/author-custom-fields.php:188
#, php-format
msgid "%2$s list navigation"
msgstr ""

#: src/core/WP_Cli.php:71
msgid "%d posts were found without author terms"
msgstr ""

#: src/core/Classes/Installer.php:329
msgid "%d/%d: Inspecting the post %d"
msgstr ""

#: src/core/Classes/Installer.php:224
msgid "%d/%d: Inspecting the user %d"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:806
msgid ""
"'span' will display as an inline element and 'div' will display as a block "
"element. To make this display into a link, select 'link' and enter the first "
"part of the URL into the 'Prefix' field."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:275
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:783
msgid "()"
msgstr ""

#: src/core/Classes/Author_Editor.php:391
#, php-format
msgid ""
"(Uses the %1s Email field %2s to find the Gravatar account if default avatar "
"is not uploaded in Authors Settings)"
msgstr ""

#: src/functions/template-tags.php:1604
msgid ", and "
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:78
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:326
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:457
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:668
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:957
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1090
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1292
msgid "100 - Thin"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:79
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:327
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:458
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:669
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:958
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1091
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1293
msgid "200 - Extra light"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:80
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:328
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:459
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:670
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:959
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1092
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1294
msgid "300 - Light"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:81
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:329
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:460
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:671
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:960
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1093
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1295
msgid "400 - Normal"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:82
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:330
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:461
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:672
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:961
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1094
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1296
msgid "500 - Medium"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:83
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:331
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:462
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:673
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:962
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1095
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1297
msgid "600 - Semi bold"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:84
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:332
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:463
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:674
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:963
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1096
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1298
msgid "700 - Bold"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:85
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:333
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:464
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:675
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:964
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1097
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1299
msgid "800 - Extra bold"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:86
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:334
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:465
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:676
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:965
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1098
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1300
msgid "900 - Black"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:274
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:782
msgid ":"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:276
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:784
msgid "[]"
msgstr ""

#: src/views/footer-base.html.php:21
msgid "About"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3583
msgid "Access denied"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2182
msgid "Action"
msgstr ""

#: src/core/CustomFieldsModel.php:61
#: src/modules/author-custom-fields/author-custom-fields.php:813
msgid "Active"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2210
msgid "Add"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:184
msgid "Add a link to author avatar"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:422
#, php-format
msgid ""
"Add a Schema.org property to this field. Examples include alumniOf, worksFor,"
" and birthPlace. %1$sClick here for documentation.%2$s"
msgstr ""

#: src/core/Plugin.php:1526
msgid "Add Author"
msgstr ""

#: src/modules/author-categories/author-categories.php:587
msgid "Add Author Category"
msgstr ""

#: src/modules/author-list/author-list.php:746
msgid "Add Author List"
msgstr ""

#: src/modules/all-in-one-seo-pack-integration/all-in-one-seo-pack-integration.php:53
msgid "Add compatibility with the All In One Seo Pack plugin"
msgstr ""

#: src/modules/divi-integration/divi-integration.php:55
msgid "Add compatibility with the Divi Theme Builder"
msgstr ""

#: src/modules/editflow-integration/editflow-integration.php:53
msgid "Add compatibility with the Edit Flow plugin"
msgstr ""

#: src/modules/elementor-integration/elementor-integration.php:59
msgid "Add compatibility with the Elementor and Elementor Pro page builder"
msgstr ""

#: src/modules/generatepress-integration/generatepress-integration.php:54
msgid "Add compatibility with the Generatepress theme"
msgstr ""

#: src/modules/genesis-integration/genesis-integration.php:54
msgid "Add compatibility with the Genesis framework"
msgstr ""

#: src/modules/rank-math-seo-integration/rank-math-seo-integration.php:55
msgid "Add compatibility with the Rank Math Seo plugin"
msgstr ""

#: src/modules/seoframework-integration/seoframework-integration.php:60
msgid "Add compatibility with The SEO Framework plugin."
msgstr ""

#: src/modules/ultimatemember-integration/ultimatemember-integration.php:55
msgid "Add compatibility with the Ultimate Member plugin"
msgstr ""

#: src/modules/ultimate-post-integration/ultimate-post-integration.php:54
msgid "Add compatibility with the Ultimate Post plugin"
msgstr ""

#: src/modules/wpengine-integration/wpengine-integration.php:53
msgid "Add compatibility with the WPEngine object cache."
msgstr ""

#: src/modules/yoast-seo-integration/yoast-seo-integration.php:54
msgid "Add compatibility with the Yoast SEO plugin"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1578
msgid "Add Custom CSS styles here..."
msgstr ""

#: src/modules/debug/debug.php:55
msgid "Add debug information for the plugin"
msgstr ""

#: src/core/Classes/Utils.php:1129
msgid "Add fields for social networks"
msgstr ""

#: src/modules/byline-migration/byline-migration.php:74
#: src/modules/byline-migration/byline-migration.php:75
msgid "Add migration option for Byline"
msgstr ""

#: src/modules/bylines-migration/bylines-migration.php:77
#: src/modules/bylines-migration/bylines-migration.php:78
msgid "Add migration option for Bylines"
msgstr ""

#: src/modules/author-list/author-list.php:279
msgid "Add New"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:409
#: src/modules/author-custom-fields/author-custom-fields.php:173
#, php-format
msgid "Add New %1$s"
msgstr ""

#: src/modules/author-categories/author-categories.php:618
msgid "Add New Author Category"
msgstr ""

#: src/core/Classes/Utils.php:1128
msgid "Add new Author Fields"
msgstr ""

#: src/core/Plugin.php:578
msgid "Add or remove authors"
msgstr ""

#: src/modules/author-list/author-list.php:473
msgid "Add Search Box to Author Lists"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:77
#: src/modules/author-boxes/author-boxes.php:81
msgid "Add support for author boxes."
msgstr ""

#: src/modules/author-categories/author-categories.php:65
#: src/modules/author-categories/author-categories.php:69
msgid "Add support for author categories."
msgstr ""

#: src/modules/author-list/author-list.php:65
#: src/modules/author-list/author-list.php:69
msgid "Add support for author list."
msgstr ""

#: src/modules/author-pages/author-pages.php:61
#: src/modules/author-pages/author-pages.php:65
msgid "Add support for author pages."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:65
#: src/modules/author-custom-fields/author-custom-fields.php:69
msgid "Add support for custom fields in the author profiles."
msgstr ""

#: src/core/Classes/Installer.php:356
msgid "Adding the author term %d to the post %d"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2988
msgid "Advanced"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2172
msgid "After All Authors"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:289
msgid "After Avatar"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2171
msgid "After Biographical Info Row"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:263
msgid "After Name"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2170
msgid "After Name Row"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2169
msgid "After View all posts Row"
msgstr ""

#: src/modules/author-list/classes/AuthorListTable.php:64
#, php-format
msgid "All %s"
msgid_plural "All %s"
msgstr[0] ""
msgstr[1] ""

#: src/core/Authors_Widget.php:181 src/core/Authors_Widget.php:412
#: src/core/Plugin.php:566 src/core/Classes/Post_Editor.php:522
msgid "All Authors"
msgstr ""

#: src/modules/author-categories/author-categories.php:338
msgid "All fields are required."
msgstr ""

#: src/modules/all-in-one-seo-pack-integration/all-in-one-seo-pack-integration.php:52
msgid "All In One Seo Pack Integration"
msgstr ""

#: src/core/Classes/Installer.php:236
msgid "All is set. No author need to be updated"
msgstr ""

#: src/core/Classes/Installer.php:375
msgid "All is set. No posts need to be updated"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1801
msgid "Allow authors to be created without a mapped user."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1755
msgid "Allow authors to be created without a real user account."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2037
msgid "Allow User to Choose"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1054
msgid "Allow users to opt out of Author Boxes"
msgstr ""

#: src/core/Classes/Author_Editor.php:1065
msgid "An author email is required when creating new User Author."
msgstr ""

#: src/core/Classes/Author_Editor.php:1035
#: src/core/Classes/Author_Editor.php:1053
msgid "An author with the name provided already exists."
msgstr ""

#: src/modules/rest-api/rest-api.php:301
msgid "An author with this slug already exists."
msgstr ""

#: src/modules/author-categories/author-categories.php:231
#: src/modules/author-categories/author-categories.php:374
#: src/modules/author-categories/author-categories.php:382
#: src/modules/author-custom-fields/author-custom-fields.php:1061
#: src/modules/author-list/author-list.php:1230
#: src/modules/author-boxes/classes/AuthorBoxesAjax.php:34
#: src/modules/author-boxes/classes/AuthorBoxesAjax.php:82
#: src/modules/author-boxes/classes/AuthorBoxesAjax.php:124
#: src/modules/author-boxes/classes/AuthorBoxesAjax.php:181
msgid "An error occured."
msgstr ""

#: src/core/Classes/Admin_Ajax.php:370
msgid "Another user with Author URL already exists."
msgstr ""

#: src/core/Plugin.php:1549
msgid ""
"Are you sure you want to create author profiles for the missed post authors?"
msgstr ""

#: src/core/Plugin.php:1561
msgid "Are you sure you want to create authors for the selected roles?"
msgstr ""

#: src/core/Plugin.php:1541
msgid ""
"Are you sure you want to delete the authors profiles mapped to users? This "
"action can't be undone."
msgstr ""

#: src/core/Plugin.php:1545
msgid ""
"Are you sure you want to delete the guest authors profiles? This action "
"can't be undone."
msgstr ""

#: src/core/Plugin.php:1528
msgid "Are you sure you want to remove this author?"
msgstr ""

#: src/core/Plugin.php:1553
msgid "Are you sure you want to update the author column for all the posts?"
msgstr ""

#: src/core/Plugin.php:1557
msgid "Are you sure you want to update the author slug for all the users?"
msgstr ""

#: src/core/Authors_Widget.php:186
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1267
msgid "Ascending"
msgstr ""

#: src/core/Plugin.php:584 src/core/Classes/Author_Editor.php:345
#: src/modules/author-boxes/author-boxes.php:1908
#: src/modules/author-categories/author-categories.php:650
#: src/modules/author-categories/author-categories.php:652
msgid "Author"
msgstr ""

#: src/modules/author-pages/author-pages.php:119
msgid "Author Bio"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:811
msgid "Author bio layout:"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:398
#: src/modules/author-boxes/author-boxes.php:500
#: src/modules/author-boxes/author-boxes.php:2717
msgid "Author Box"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:476
msgid "Author Box added."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1523
msgid "Author Box Background Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1502
msgid "Author Box Border Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1517
msgid "Author Box Border Radius (px)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1484
msgid "Author Box Border Style"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1478
msgid "Author Box Border Width (px)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1529
msgid "Author Box Color"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1016
msgid "Author Box Editor"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1436
msgid "Author Box Margin Bottom"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1442
msgid "Author Box Margin Left"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1448
msgid "Author Box Margin Right"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1430
msgid "Author Box Margin Top"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1460
msgid "Author Box Padding Bottom"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1466
msgid "Author Box Padding Left"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1472
msgid "Author Box Padding Right"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1454
msgid "Author Box Padding Top"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:402
#, php-format
msgctxt "Author Box post type name"
msgid "%2$s"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:999
msgid "Author Box Preview"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:477
msgid "Author Box saved."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1553
msgid "Author Box Shadow Blur"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1535
msgid "Author Box Shadow Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1541
msgid "Author Box Shadow Horizontal Offset"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1559
msgid "Author Box Shadow Spread"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1547
msgid "Author Box Shadow Vertical Offset"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:478
msgid "Author Box submitted."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:474
#: src/modules/author-boxes/author-boxes.php:475
msgid "Author Box updated."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1508
msgid "Author Box Width (%)"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:76
#: src/modules/author-boxes/author-boxes.php:399
#: src/modules/author-boxes/author-boxes.php:457
#: src/modules/author-boxes/author-boxes.php:458
#: src/modules/author-boxes/author-boxes.php:501
#: src/modules/multiple-authors/multiple-authors.php:2983
msgid "Author Boxes"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1106
#: src/modules/author-categories/author-categories.php:64
#: src/modules/author-categories/author-categories.php:193
#: src/modules/author-categories/author-categories.php:194
#: src/modules/author-categories/author-categories.php:563
#: src/modules/author-list/author-list.php:444
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:253
#: src/modules/author-categories/classes/AuthorCategoriesTable.php:29
msgid "Author Categories"
msgstr ""

#: src/modules/author-categories/classes/AuthorCategoriesTable.php:153
msgid "Author categories deleted successfully."
msgstr ""

#: src/modules/author-categories/classes/AuthorCategoriesTable.php:181
msgid "Author categories disabled."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:269
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:778
msgid "Author Categories Divider"
msgstr ""

#: src/modules/author-categories/classes/AuthorCategoriesTable.php:167
msgid "Author categories enabled."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:259
msgid "Author Categories Position"
msgstr ""

#: src/core/Classes/Author_Editor.php:410
#: src/modules/author-categories/classes/AuthorCategoriesTable.php:28
msgid "Author Category"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:198
msgid "Author Category Boxes"
msgstr ""

#: src/modules/author-categories/classes/AuthorCategoriesTable.php:156
msgid "Author category deleted successfully."
msgstr ""

#: src/modules/author-categories/classes/AuthorCategoriesTable.php:184
msgid "Author category disabled."
msgstr ""

#: src/modules/author-categories/classes/AuthorCategoriesTable.php:170
msgid "Author category enabled."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:721
msgid "Author Category Group Bottom Space"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:625
msgid "Author Category Group Display Style (Laptop)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:637
msgid "Author Category Group Display Style (Mobile)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:714
msgid "Author Category Group Font Size"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:727
msgid "Author Category Group Right Space"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:698
msgid "Author Category Group Title Prefix"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:706
msgid "Author Category Group Title Suffix"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:615
msgid "Author Category Grouping Option"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:591
msgid "Author Category Layout"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:681
msgid "Author Category Title HTML Tag"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:661
msgid "Author Category Weight"
msgstr ""

#: src/modules/author-categories/author-categories.php:264
#: src/modules/author-categories/author-categories.php:343
msgid "Author category with this name already exist."
msgstr ""

#: src/core/Classes/Author_Editor.php:817
msgid "Author Email"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:162
msgid "Author Field"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1110
#: src/modules/author-custom-fields/author-custom-fields.php:64
#: src/modules/author-custom-fields/author-custom-fields.php:163
#: src/modules/author-custom-fields/author-custom-fields.php:221
#: src/modules/author-custom-fields/author-custom-fields.php:222
msgid "Author Fields"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:649
msgid "Author Grouping Title Option"
msgstr ""

#: src/modules/author-list/author-list.php:566
msgid "Author Index List"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1398
msgid "Author inline display"
msgstr ""

#: src/modules/author-list/author-list.php:64
#: src/modules/author-list/author-list.php:714
msgid "Author List"
msgstr ""

#: src/modules/author-list/author-list.php:243
msgid "Author List deleted successfully."
msgstr ""

#: src/modules/author-list/author-list.php:251
msgid "Author List moved to the Trash."
msgstr ""

#: src/modules/author-list/author-list.php:247
msgid "Author List restored from the Trash."
msgstr ""

#: src/modules/author-list/author-list.php:196
#: src/modules/author-list/author-list.php:197
#: src/modules/author-list/author-list.php:273
#: src/modules/author-list/author-list.php:761
msgid "Author Lists"
msgstr ""

#: src/core/Classes/Admin_Ajax.php:405
msgid "Author name is required"
msgstr ""

#: src/modules/rest-api/rest-api.php:404 src/modules/rest-api/rest-api.php:420
#: src/modules/rest-api/rest-api.php:439
msgid "Author not found."
msgstr ""

#: src/modules/author-pages/author-pages.php:120
msgid "Author Page Title"
msgstr ""

#: src/modules/author-pages/author-pages.php:60
#: src/modules/author-pages/author-pages.php:106
#: src/modules/author-pages/author-pages.php:107
#: src/modules/multiple-authors/multiple-authors.php:2985
msgid "Author Pages"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:830
msgid "Author pages excerpt ellipsis:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:778
msgid "Author pages layout:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:767
msgid "Author pages posts limit:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:841
msgid "Author pages title header:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:849
msgid "Author post title header:"
msgstr ""

#: src/modules/author-list/author-list.php:474
msgid ""
"Author Pro allows you to add a search box to the Authors List. You can also "
"show a dropdown menu that allows users to search on specific author fields."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:340
#: src/modules/multiple-authors/multiple-authors.php:341
msgid "Author Profile"
msgstr ""

#: src/core/Classes/Term_Editor.php:43
msgid "Author profile deleted."
msgstr ""

#: src/core/Classes/Term_Editor.php:44
msgid "Author profile updated."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2984
msgid "Author Profiles"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1711
msgid ""
"Author profiles can be mapped to WordPress user accounts. This option allows "
"you to automatically create author profiles when users are created in these "
"roles. You can also do this for existing users by clicking the \"Create "
"missed authors from role\" button in the Maintenance tab."
msgstr ""

#: src/modules/author-list/author-list.php:520
msgid "Author Recent List"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1118
msgid "Author Recent Posts"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1406
msgid "Author Row Prefix"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1414
msgid "Author Row Suffix"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1422
msgid "Author Separator"
msgstr ""

#: src/core/Classes/Author_Editor.php:49
#: src/modules/author-list/author-list.php:442
msgid "Author Type"
msgstr ""

#: src/core/Classes/Term_Editor.php:79
#: src/modules/multiple-authors/multiple-authors.php:3081
msgid "Author URL"
msgstr ""

#: src/core/Classes/Admin_Ajax.php:357
msgid "Author URL cannot be empty."
msgstr ""

#: src/modules/rest-api/rest-api.php:380
msgid "Author was created but could not be retrieved."
msgstr ""

#: src/core/Authors_Widget.php:141
msgid "Author's search box field (Seperate multiple fields by comma(','))"
msgstr ""

#: src/modules/author-list/classes/AuthorListTable.php:40
#: src/modules/author-list/classes/AuthorListTable.php:41
msgid "AuthorList"
msgstr ""

#: src/core/Authors_Widget.php:138 src/core/Plugin.php:860
#: src/core/Classes/Post_Editor.php:95 src/core/Classes/Post_Editor.php:207
#: src/core/Traits/Author_box.php:195
#: src/modules/author-categories/author-categories.php:651
#: src/modules/author-list/author-list.php:443
#: src/modules/editflow-integration/editflow-integration.php:114
#: src/modules/multiple-authors/multiple-authors.php:325
#: src/modules/multiple-authors/multiple-authors.php:326
#: src/modules/multiple-authors/multiple-authors.php:374
#: src/modules/multiple-authors/multiple-authors.php:375
#: src/modules/multiple-authors/multiple-authors.php:394
#: src/modules/multiple-authors/multiple-authors.php:1273
#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:82
#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:163
#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:353
#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:434
#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:517
#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:605
msgid "Authors"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1306
msgid "Authors Box"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1387
msgid "Authors Data"
msgstr ""

#: src/modules/debug/debug.php:98
msgid "Authors Debug"
msgstr ""

#: src/core/Classes/Term_Editor.php:47
msgid "Authors deleted."
msgstr ""

#: src/modules/default-layouts/default-layouts.php:127
msgid "Authors Index"
msgstr ""

#: src/core/Authors_Widget.php:23
#: src/modules/multiple-authors/multiple-authors.php:1456
msgid "Authors List"
msgstr ""

#: src/modules/author-list/author-list.php:464
msgid ""
"Authors Pro allows you to add extra features to the Authors List. These "
"features include pagination, choose the order of authors, and much more."
msgstr ""

#: src/modules/default-layouts/default-layouts.php:128
msgid "Authors Recent"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:57
msgid "Authors Settings"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:604
msgid "Automatically create author profiles:"
msgstr ""

#: src/core/Classes/Author_Editor.php:316
#: src/modules/author-boxes/author-boxes.php:1090
msgid "Avatar"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:219
msgid "Avatar Border Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:225
msgid "Avatar Border Radius (%)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:196
msgid "Avatar Border Style"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:213
msgid "Avatar Border Width"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:190
msgid "Avatar size (px)"
msgstr ""

#: src/core/Classes/Author_Editor.php:383
msgid "Avatar Source"
msgstr ""

#: src/core/Plugin.php:585
msgid "Back to Authors"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1066
#: src/modules/author-custom-fields/author-custom-fields.php:753
msgid "Banner"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:264
msgid "Before Name"
msgstr ""

#: src/core/Classes/Author_Editor.php:362
#: src/modules/author-boxes/author-boxes.php:1098
msgid "Biographical Info"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1141
msgid "Biographical Info Alignment"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1064
msgid "Biographical Info Character Limit"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1154
msgid "Biographical Info Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1128
msgid "Biographical Info Decoration"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1161
msgid "Biographical Info HTML Tag"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1077
msgid "Biographical Info Line Height (px)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:800
msgid "Biographical Info Row"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1071
msgid "Biographical Info Size"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1116
msgid "Biographical Info Style"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1103
msgid "Biographical Info Transform"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1083
msgid "Biographical Info Weight"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:631
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:643
msgid "Block"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:620
msgid "Block Grouping"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:77
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:325
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:456
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:667
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:956
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1089
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1291
msgid "Bold"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1122
msgid "Box Layout"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1086
msgid "Box Title"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:129
msgid "Box Title Alignment"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:53
msgid "Box Title Bottom space"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:142
msgid "Box Title Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:116
msgid "Box Title Decoration"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:148
msgid "Box Title HTML Tag"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:65
msgid "Box Title Line Height (px)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:59
msgid "Box Title Size"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:104
msgid "Box Title Style"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:47
msgid "Box Title Text (Plural)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:41
msgid "Box Title Text (Single)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:91
msgid "Box Title Transform"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:71
msgid "Box Title Weight"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:28
msgid "Boxed"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:599
msgid "Boxed (Categories)"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1087
msgid "Boxed legacy layout Author Box:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:29
msgid "Boxed Right"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:597
msgid "Built-in Layout"
msgstr ""

#: src/modules/ultimate-post-integration/ultimate-post-integration.php:104
msgid "By"
msgstr ""

#: src/modules/author-categories/classes/AuthorCategoriesTable.php:459
msgid "Cancel"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:98
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:346
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:477
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:979
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1110
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1312
msgid "Capitalize"
msgstr ""

#: src/modules/author-categories/author-categories.php:280
msgid "Category added."
msgstr ""

#: src/modules/author-categories/author-categories.php:403
msgid "Category order updated."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:135
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:383
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:514
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1022
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1147
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1349
msgid "Center"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:30
msgid "Centered"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1095
msgid "Centered legacy layout Author Box:"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2434
#, php-format
msgid "Change %1s"
msgstr ""

#: src/core/Classes/Legacy/LegacyPlugin.php:288
msgid "Cheatin&#8217; uh?"
msgstr ""

#: src/core/Plugin.php:579
msgid "Choose from the most used Authors"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2732
#: src/modules/multiple-authors/multiple-authors.php:2738
#: src/modules/multiple-authors/multiple-authors.php:2744
#: src/modules/multiple-authors/multiple-authors.php:2751
msgid "Click here for documentation"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1830
msgid "Click here for more details."
msgstr ""

#: src/modules/modules-settings/modules-settings.php:313
msgid "Click here to install PublishPress Blocks"
msgstr ""

#: src/core/Plugin.php:863
msgid "Click on an author to change them. Drag to change their order."
msgstr ""

#: src/core/Plugin.php:1537
msgid ""
"Click on an author to change them. Drag to change their order. Click on "
"<strong>Remove</strong> to remove them."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2295
msgid ""
"Click the \"Generate Template\" button under the text area. Wait for the "
"code to be generated."
msgstr ""

#: src/core/Plugin.php:1532
msgid "Click to change this author, or drag to change their position"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1637
#: src/modules/multiple-authors/multiple-authors.php:1638
msgid "Click To Copy!"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:4487
msgid "Click to run the update now"
msgstr ""

#: src/core/Classes/Utils.php:74
msgid "Co-Authors Plus must be installed and active."
msgstr ""

#: src/modules/author-categories/author-categories.php:659
#: src/modules/author-categories/author-categories.php:661
msgid "Coauthor"
msgstr ""

#: src/modules/author-categories/author-categories.php:660
msgid "Coauthors"
msgstr ""

#: src/modules/byline-migration/byline-migration.php:145
#: src/modules/bylines-migration/bylines-migration.php:147
#: src/modules/multiple-authors/multiple-authors.php:3953
msgid "Collecting data for the migration..."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3885
#: src/modules/multiple-authors/multiple-authors.php:3916
msgid "Collecting data..."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:712
msgid "Color scheme:"
msgstr ""

#: src/templates/parts/author-pages-grid.php:141
#: src/templates/parts/author-pages-list.php:130
msgid "Comment counts"
msgstr ""

#: src/core/Classes/Legacy/LegacyPlugin.php:281
msgid "Configure"
msgstr ""

#: src/modules/author-list/author-list.php:463
msgid "Configure Author List Options"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2298
msgid ""
"Congratulations. Your can now choose your template inside the PublishPress "
"Authors Settings."
msgstr ""

#: src/views/footer-base.html.php:30
msgid "Contact"
msgstr ""

#: src/core/Classes/Author_Editor.php:896
msgid "Convert to Guest Author With User Account"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2267
#: src/modules/author-boxes/author-boxes.php:2339
msgid "Copied to Clipboard!"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1636
msgid "Copied!"
msgstr ""

#: src/modules/byline-migration/byline-migration.php:153
#: src/modules/byline-migration/byline-migration.php:168
msgid "Copy Byline Data"
msgstr ""

#: src/modules/bylines-migration/bylines-migration.php:155
#: src/modules/bylines-migration/bylines-migration.php:170
msgid "Copy Bylines Data"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2770
msgid "Copy Co-Authors Plus Data"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3958
msgid "Copy Co-Authors Plus data"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2297
msgid "Copy the generated code and paste it inside the newly created file."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2263
#: src/modules/author-boxes/author-boxes.php:2334
msgid "Copy to Clipboard"
msgstr ""

#: src/modules/byline-migration/byline-migration.php:149
#: src/modules/bylines-migration/bylines-migration.php:151
#: src/modules/multiple-authors/multiple-authors.php:3957
msgid "Copying authors' data..."
msgstr ""

#: src/core/Classes/Author_Editor.php:791
msgid ""
"Create an author profile and a linked user account. This account will be in "
"the \"Guest Author\" role and will not be able to login to the WordPress "
"dashboard or update their profile."
msgstr ""

#: src/core/Classes/Author_Editor.php:785
msgid ""
"Create an author profile for a current user account. You can select users "
"who are allowed to edit posts. This user will be able to update their "
"profile."
msgstr ""

#: src/core/Classes/Author_Editor.php:798
msgid ""
"Create an author profile with no linked user account. This option is "
"lightweight but may have compatibility issues with other themes and plugins."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2296
#, php-format
msgid ""
"Create an empty php template file with your desired file slug in the %1s "
"/publishpress-authors/author-boxes/ %2s folder of your theme. %3s For "
"example, the file can be located here: %4s /wp-content/themes/%5syour-theme-"
"name%6s/publishpress-authors/author-boxes/my-first-custom-author-template."
"php %7s ."
msgstr ""

#: src/core/Classes/Utils.php:1131
msgid "Create Author Boxes with authors organized in categories"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2757
#: src/modules/multiple-authors/multiple-authors.php:2759
msgid "Create Author Categories"
msgstr ""

#: src/core/Classes/Author_Editor.php:175
msgid "Create Author Profile"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2733
msgid "Create missing PublishPress Authors profiles"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2739
msgid "Create PublishPress Authors profiles"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2731
msgid "Create PublishPress Authors Profiles for all post authors"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2737
msgid "Create PublishPress Authors Profiles for all users in a role"
msgstr ""

#: src/core/Classes/Installer.php:343
msgid "Creating author term for the user %d"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3955
msgid "Creating missed post authors...."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1126
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1577
msgid "Custom CSS"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:307
msgid "Custom Field"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:182
#, php-format
msgctxt "custom field post type menu name"
msgid "%2$s"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:166
#, php-format
msgctxt "Custom Field post type name"
msgid "%2$s"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:283
msgid "Custom Field published."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:284
msgid "Custom Field saved."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:285
msgid "Custom Field submitted."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:281
#: src/modules/author-custom-fields/author-custom-fields.php:282
msgid "Custom Field updated."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:308
msgid "Custom Fields"
msgstr ""

#: src/core/Classes/Author_Editor.php:399
msgid "Custom image"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:418
#, php-format
msgctxt "custom layout post type menu name"
msgid "%2$s"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:202
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1220
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1490
msgid "Dashed"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1253
msgid "Date"
msgstr ""

#: src/modules/byline-migration/byline-migration.php:152
msgid "Deactivate Byline"
msgstr ""

#: src/modules/bylines-migration/bylines-migration.php:154
msgid "Deactivate Bylines"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3961
msgid "Deactivate Co-Authors Plus"
msgstr ""

#: src/modules/byline-migration/byline-migration.php:150
msgid "Deactivating Byline..."
msgstr ""

#: src/modules/bylines-migration/bylines-migration.php:152
msgid "Deactivating Bylines..."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3959
msgid "Deactivating Co-uthors Plus..."
msgstr ""

#: src/modules/debug/debug.php:54
msgid "Debug"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:75
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:95
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:108
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:120
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:133
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:323
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:343
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:356
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:368
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:381
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:454
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:474
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:487
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:499
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:512
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:629
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:641
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:665
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:954
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:976
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:991
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1005
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1020
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1087
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1107
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1120
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1132
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1145
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1289
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1309
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1322
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1334
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1347
msgid "Default"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:195
msgid "Default Author Box"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:626
msgid "Default author for new posts:"
msgstr ""

#: src/core/Classes/Author_Editor.php:389
#: src/modules/multiple-authors/multiple-authors.php:1076
msgid "Default Avatar"
msgstr ""

#: src/modules/default-layouts/default-layouts.php:55
msgid "Default Layouts"
msgstr ""

#: src/core/Authors_Widget.php:372
msgid "Default Search"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2209
#: src/modules/author-boxes/author-boxes.php:2238
#: src/modules/author-categories/classes/AuthorCategoriesTable.php:125
#: src/modules/author-categories/classes/AuthorCategoriesTable.php:373
msgid "Delete"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2780
msgid "Delete all authors mapped to users"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2787
msgid "Delete all guest authors"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2785
msgid "Delete Guest Authors"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2778
msgid "Delete Mapped Authors"
msgstr ""

#: src/modules/author-list/classes/AuthorListTable.php:384
msgid "Delete Permanently"
msgstr ""

#: src/core/Authors_Widget.php:187
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1268
msgid "Descending"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:465
msgid "Description"
msgstr ""

#: src/core/Classes/Utils.php:1160
msgid "Detailed documentation is also available on the plugin website."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:365
msgid "Details"
msgstr ""

#: src/modules/author-categories/classes/AuthorCategoriesTable.php:124
msgid "Disable Categories"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:688
msgid "Disable the \"Authors\" box when using \"Quick Edit\":"
msgstr ""

#: src/core/Classes/Post_Editor.php:464
msgid "Disable the default author display under this post"
msgstr ""

#: src/core/CustomFieldsModel.php:62
#: src/modules/author-custom-fields/author-custom-fields.php:817
#: src/modules/author-categories/classes/AuthorCategoriesTable.php:392
msgid "Disabled"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1214
#: src/modules/settings/settings.php:275
#, php-format
msgid ""
"Disabled because add_post_type_support('%1$s', '%2$s') is included in a "
"loaded file."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:4419
#: src/modules/multiple-authors/multiple-authors.php:4493
msgid "Dismiss"
msgstr ""

#: src/core/Widget.php:48
msgid "Display a list of authors for the current post."
msgstr ""

#: src/core/Authors_Widget.php:132
msgid "Display All Authors (including those who have not written any posts)"
msgstr ""

#: src/core/Authors_Widget.php:29
msgid "Display authors list."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1094
#: src/modules/multiple-authors/multiple-authors.php:1065
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:285
msgid "Display Name"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:377
msgid "Display Name Alignment"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:390
msgid "Display Name Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:364
msgid "Display Name Decoration"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:396
msgid "Display Name HTML Tag"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:313
msgid "Display Name Line Height (px)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:283
msgid "Display Name Position"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:295
msgid "Display Name Prefix"
msgstr ""

#: src/core/Plugin.php:1527 src/core/Classes/Author_Editor.php:47
msgid "Display name publicly as"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:307
msgid "Display Name Size"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:352
msgid "Display Name Style"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:301
msgid "Display Name Suffix"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:339
msgid "Display Name Transform"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:319
msgid "Display Name Weight"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:158
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:407
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:549
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:685
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:814
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1171
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1378
msgid "div"
msgstr ""

#: src/modules/divi-integration/divi-integration.php:54
msgid "Divi Integration"
msgstr ""

#: src/core/Classes/Author_Editor.php:418
msgid "Do not show this profile publicly"
msgstr ""

#: src/views/footer-base.html.php:26
msgid "Documentation"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2303
msgid "documentation page"
msgstr ""

#: src/core/CustomFieldsModel.php:116
msgid "dofollow"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3918
msgid "Done! %d authors were updated."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3887
msgid "Done! %d posts were updated."
msgstr ""

#: src/modules/byline-migration/byline-migration.php:147
msgid "Done! Byline data was copied and you can deactivate the plugin."
msgstr ""

#: src/modules/byline-migration/byline-migration.php:151
msgid "Done! Byline is deactivated."
msgstr ""

#: src/modules/bylines-migration/bylines-migration.php:149
msgid "Done! Bylines data was copied and you can deactivate the plugin."
msgstr ""

#: src/modules/bylines-migration/bylines-migration.php:153
msgid "Done! Bylines is deactivated."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3962
msgid "Done! Co-Authors Plus data was copied."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3960
msgid "Done! Co-Authors Plus is deactivated."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:201
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1219
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1489
msgid "Dotted"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:204
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1222
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1493
msgid "Double"
msgstr ""

#: src/core/Classes/Post_Editor.php:305
#, php-format
msgid "Drag-and-drop Authors to add them to the %s category"
msgstr ""

#: src/modules/author-list/author-list.php:840
msgid "Dynamic Shortcode"
msgstr ""

#: src/core/Plugin.php:1525
#: src/modules/author-list/classes/AuthorListTable.php:344
msgid "Edit"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:410
#: src/modules/author-custom-fields/author-custom-fields.php:174
#, php-format
msgid "Edit %1$s"
msgstr ""

#: src/core/Plugin.php:569
msgid "Edit Author"
msgstr ""

#: src/modules/author-list/author-list.php:746
msgid "Edit Author List"
msgstr ""

#: src/core/Classes/Author_Editor.php:161
#: src/core/Classes/Author_Editor.php:209
msgid "Edit Author Profile"
msgstr ""

#: src/modules/editflow-integration/editflow-integration.php:52
msgid "Edit Flow Integration"
msgstr ""

#: src/core/Plugin.php:519
msgid "Edit My Author Profile"
msgstr ""

#: src/core/Classes/Author_Editor.php:147
#: src/core/Classes/Author_Editor.php:224
msgid "Edit User"
msgstr ""

#: src/modules/author-categories/author-categories.php:677
#: src/modules/author-categories/author-categories.php:679
msgid "Editor"
msgstr ""

#: src/modules/author-categories/author-categories.php:678
msgid "Editors"
msgstr ""

#: src/modules/elementor-integration/elementor-integration.php:58
msgid "Elementor Integration"
msgstr ""

#: src/core/Classes/Author_Editor.php:48 src/core/Classes/Author_Editor.php:378
#: src/modules/author-custom-fields/author-custom-fields.php:931
msgid "Email"
msgstr ""

#: src/core/CustomFieldsModel.php:44
msgid "Email address"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:277
msgid "Empty Space"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:584
msgid "Enable Author Grouping"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:756
msgid "Enable author pages:"
msgstr ""

#: src/core/Authors_Widget.php:136
msgid "Enable author's search box"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1058
msgid "Enable Biographical Link"
msgstr ""

#: src/modules/author-categories/classes/AuthorCategoriesTable.php:123
msgid "Enable Categories"
msgstr ""

#: src/modules/author-categories/author-categories.php:614
#: src/modules/author-categories/classes/AuthorCategoriesTable.php:80
#: src/modules/author-categories/classes/AuthorCategoriesTable.php:450
msgid "Enable Category"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1032
msgid "Enable Guest Author With No User Account"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1043
msgid "Enable Guest Author With User Account"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:588
msgid "Enable PublishPress Authors for these post types:"
msgstr ""

#: src/modules/author-categories/classes/AuthorCategoriesTable.php:390
msgid "Enabled"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:260
msgid "Enabled features"
msgstr ""

#: src/core/Classes/Utils.php:1125
msgid "Enhance the power of PublishPress Authors with the Pro version:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1588
msgid "Enter class name without dot(.)"
msgstr ""

#: src/modules/author-categories/author-categories.php:592
msgid "Enter the Author Category name when it's a single author"
msgstr ""

#: src/modules/author-categories/author-categories.php:597
msgid "Enter the Author Category name when there are more than 1 author"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1415
msgid ""
"Enter the text that should be added after authors. This field accepts basic "
"HTML."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:707
msgid ""
"Enter the text that should be added after group title. This field accepts "
"basic HTML."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1407
msgid ""
"Enter the text that should be added before authors. This field accepts basic "
"HTML."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:699
msgid ""
"Enter the text that should be added before group title. This field accepts "
"basic HTML."
msgstr ""

#: src/core/Classes/Term_Editor.php:45
msgid "Error adding author account."
msgstr ""

#: src/modules/author-categories/author-categories.php:293
msgid "Error inserting new author category."
msgstr ""

#: src/core/Classes/Term_Editor.php:46
msgid "Error updating author profile."
msgstr ""

#: src/modules/author-categories/author-categories.php:366
msgid "Error updating category data."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3756
msgid "Error. Author terms could not be reseted."
msgstr ""

#: src/modules/byline-migration/byline-migration.php:146
#: src/modules/bylines-migration/bylines-migration.php:148
#: src/modules/multiple-authors/multiple-authors.php:3954
msgid "Error: "
msgstr ""

#: src/modules/author-list/author-list.php:451
msgid "Exclude Authors"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:608
msgid "Existing Author Boxes"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1130
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1624
msgid "Export"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2252
msgid "Export Editor Settings"
msgstr ""

#: src/core/Classes/Utils.php:1130
msgid "Extra features for Author Lists"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:1197
msgid "Facebook"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:637
msgid "Fallback user for Guest Authors:"
msgstr ""

#: src/core/Classes/Utils.php:1134
msgid "Fast, professional support"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:879
msgid "Featured image custom height:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:868
msgid "Featured image custom width:"
msgstr ""

#: src/modules/author-list/author-list.php:424
msgid "Featured Image Size"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:251
msgid "Features"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:252
msgid "Feel free to select only the features you need."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:838
msgid "Field Display"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:848
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:892
msgid "Field Icon"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:844
msgid "Field Icon + Prefix + Value + Suffix"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:1092
msgid "Field Order updated."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesAjax.php:67
#, php-format
msgid ""
"Field Order updated. %1sClick here%2s to reload this page to see new order "
"changes."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:762
msgid "Field Output"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:846
msgid "Field Prefix"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:849
msgid "Field Prefix + Value + Suffix"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:384
msgid "Field Slug"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:375
msgid "Field Status"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:847
msgid "Field Suffix"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:932
msgid "Field Text"
msgstr ""

#: src/core/Plugin.php:1579
msgid "Field title is required"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:396
msgid "Field Type"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:845
msgid "Field Value"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:423
#: src/modules/author-custom-fields/author-custom-fields.php:187
#, php-format
msgid "Filter %2$s list"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3886
#: src/modules/multiple-authors/multiple-authors.php:3917
msgid "Finishing the process..."
msgstr ""

#: src/core/Authors_Widget.php:192 src/core/Classes/Author_Editor.php:368
#: src/modules/author-custom-fields/author-custom-fields.php:915
#: src/modules/multiple-authors/multiple-authors.php:2040
msgid "First Name"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2038
msgid "First Name Last Name"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:630
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:642
msgid "Flex"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:737
msgid "Font Awesome icons:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1535
#, php-format
msgid ""
"For authors_index layout, you can group user by profile field by using %1$s ."
msgstr ""

#: src/modules/author-list/author-list.php:414
msgid "For authors_index layout, you can group user by profile fields."
msgstr ""

#: src/modules/author-list/author-list.php:425
msgid "For authors_recent layout, you can select the featured image size.."
msgstr ""

#: src/modules/author-categories/author-categories.php:603
#, php-format
msgid ""
"For example, when this value is set to reviewedBy, all users under this "
"category will be added to post reviewedBy property. You can read more %1$s "
"in this guide.%2$s"
msgstr ""

#: src/core/Classes/Author_Editor.php:313
#: src/modules/author-list/author-list.php:316
#: src/modules/author-pages/author-pages.php:117
#: src/modules/multiple-authors/multiple-authors.php:2982
msgid "General"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2328
msgid "Generate Template"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1138
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1661
msgid "Generate Theme Template"
msgstr ""

#: src/modules/generatepress-integration/generatepress-integration.php:53
msgid "Generatepress Integration"
msgstr ""

#: src/modules/genesis-integration/genesis-integration.php:53
msgid "Genesis Integration"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1865
msgid "Grid"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:789
msgid "Grid layout column:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:205
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1223
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1494
msgid "Groove"
msgstr ""

#: src/modules/author-list/author-list.php:413
msgid "Group By"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:4882
msgid "Guest Author cannot login on the site."
msgstr ""

#: src/core/Classes/Author_Editor.php:121
#: src/core/Classes/Author_Editor.php:797
#: src/modules/author-list/author-list.php:1050
msgid "Guest Author With No User Account"
msgstr ""

#: src/core/Classes/Author_Editor.php:117
#: src/core/Classes/Author_Editor.php:790
#: src/modules/author-list/author-list.php:1049
msgid "Guest Author With User Account"
msgstr ""

#: src/modules/rest-api/rest-api.php:293
msgid "Guest author with user accounts is not enabled."
msgstr ""

#: src/core/Authors_Widget.php:182
msgid "Guest Authors"
msgstr ""

#: src/modules/rest-api/rest-api.php:285
msgid "Guest authors without user accounts is not enabled."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1893
#: src/modules/multiple-authors/multiple-authors.php:1925
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:152
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:401
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:543
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:688
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:816
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1165
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1372
msgid "H1"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1894
#: src/modules/multiple-authors/multiple-authors.php:1926
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:153
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:402
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:544
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:689
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:817
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1166
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1373
msgid "H2"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1895
#: src/modules/multiple-authors/multiple-authors.php:1927
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:154
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:403
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:545
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:690
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:818
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1167
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1374
msgid "H3"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1896
#: src/modules/multiple-authors/multiple-authors.php:1928
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:155
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:404
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:546
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:691
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:819
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1168
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1375
msgid "H4"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1897
#: src/modules/multiple-authors/multiple-authors.php:1929
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:156
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:405
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:547
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:692
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:820
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1169
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1376
msgid "H5"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1898
#: src/modules/multiple-authors/multiple-authors.php:1930
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:157
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:406
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:548
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:693
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:821
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1170
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1377
msgid "H6"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:760
#, php-format
msgid "Hide %1s"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:653
msgid "Hide Title"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2292
msgid "How to generate and use a theme template file"
msgstr ""

#. Author URI of the plugin
msgid "https://publishpress.com"
msgstr ""

#. URI of the plugin
msgid "https://wordpress.org/plugins/publishpress-authors/"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1256
msgid "ID"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2732
msgid ""
"If a WordPress user is the author of a post, but does not have a "
"PublishPress Authors profile, this will automatically create a profile for "
"them."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1825
msgid ""
"If enabled, PublishPress Authors will replace the default WordPress author "
"pages."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1732
msgid ""
"If the Author is mapped to a WordPress user, this will display the authors' "
"\"Display name\" and their \"Username\". The default is to show only the "
"\"Display name\". Showing the \"Username\" is useful if you have several "
"authors with similar names."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1358
#, php-format
msgid ""
"If you are having problems showing PublishPress Authors on author profile "
"pages, you can use this shortcode below. The argument %s forces the plugin "
"to retrieve the Author from the profile page and not any other posts on the "
"same screen."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1525
#, php-format
msgid ""
"If you are using the authors_recent layout, you can define the number of "
"columns by using %1$s ."
msgstr ""

#: src/views/footer-base.html.php:14
#, php-format
msgid "If you like %1$s please leave us a %2$s rating. Thank you!"
msgstr ""

#: src/core/Plugin.php:718
#, php-format
msgid "If you like %s please leave us a %s rating. Thank you!"
msgstr ""

#: src/core/Classes/Utils.php:1149
msgid "If you need help or have a new feature request, let us know."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2636
msgid ""
"If you only have Guest Authors selected for a post, this user may be used as "
"a fallback. WordPress sometimes requires a WordPress user to be assigned to "
"each post. This user will not be visible on the front of your site."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1134
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1643
msgid "Import"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2283
msgid "Import Data"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2272
msgid "Import Editor Settings"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:290
msgid "In front of Avatar"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:31
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:632
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:644
msgid "Inline"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1111
msgid "Inline avatar legacy layout Author Box:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:619
msgid "Inline Grouping"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1103
msgid "Inline legacy layout Author Box:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:32
msgid "Inline with Avatars"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:207
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1225
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1496
msgid "Inset"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:1215
msgid "Instagram"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:308
msgid "Install this plugin to showcase content by your Authors."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2282
msgid "Invalid data"
msgstr ""

#: src/modules/author-list/author-list.php:1243
msgid "Invalid form"
msgstr ""

#: src/modules/author-categories/author-categories.php:392
msgid "Invalid form data."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3579
msgid "Invalid nonce"
msgstr ""

#: src/core/Plugin.php:1577
msgid "is required"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:4413
msgid "It looks like you have Co-Authors Plus installed."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:110
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:358
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:489
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:993
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1122
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1324
msgid "Italic"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:947
msgid "Job Title"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:137
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:385
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:516
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1024
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1149
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1351
msgid "Justify"
msgstr ""

#: src/modules/author-list/author-list.php:362
msgid "Large-sized image (1024px by 1024px)"
msgstr ""

#: src/core/Authors_Widget.php:193 src/core/Classes/Author_Editor.php:373
#: src/modules/author-custom-fields/author-custom-fields.php:923
#: src/modules/multiple-authors/multiple-authors.php:2041
msgid "Last Name"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2039
msgid "Last Name First Name"
msgstr ""

#: src/core/Authors_Widget.php:131 src/core/Widget.php:123
#: src/modules/author-list/author-list.php:393
#: src/modules/author-pages/author-pages.php:118
#: src/modules/author-list/classes/AuthorListTable.php:151
msgid "Layout"
msgstr ""

#: src/modules/author-list/author-list.php:403
msgid "Layout Columns"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1033
msgid "Layout Slug"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1586
msgid "Layout Wrapper Class Name"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:696
msgid "Layout:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:134
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:382
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:513
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1021
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1146
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1348
msgid "Left"
msgstr ""

#: src/core/Authors_Widget.php:137
msgid "Limits per page"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:123
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:371
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:502
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1008
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1135
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1337
msgid "Line Through"
msgstr ""

#: src/core/CustomFieldsModel.php:43
msgid "Link"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:813
msgid "link"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:183
msgid "Link Avatar"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:446
msgid "Link Rel"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:1224
msgid "LinkedIn"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1864
msgid "List"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:601
msgid "List Authors Block (Categories)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:602
msgid "List Authors Inline (Categories)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:97
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:345
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:476
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:978
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1109
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1311
msgid "Lowercase"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2987
msgid "Maintenance"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3767
msgid "Maintenance completed successfully."
msgstr ""

#: src/core/Classes/Author_Editor.php:355
msgid "Mapped User"
msgstr ""

#: src/core/Classes/Admin_Ajax.php:347 src/core/Classes/Author_Editor.php:1101
msgid "Mapped user is required."
msgstr ""

#: src/modules/author-list/author-list.php:363
msgid "Medium-large image (768px)"
msgstr ""

#: src/modules/author-list/author-list.php:364
msgid "Medium-sized image (300px by 300px)"
msgstr ""

#: src/modules/byline-migration/byline-migration.php:73
msgid "Migrate Byline Data"
msgstr ""

#: src/modules/bylines-migration/bylines-migration.php:76
msgid "Migrate Bylines Data"
msgstr ""

#: src/core/Plugin.php:1662
msgid "Mine"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1254
msgid "Modified date"
msgstr ""

#: src/core/CustomFieldsModel.php:41
msgid "Multiline text"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:74
msgid "Multiple Authors"
msgstr ""

#: src/core/Authors_Widget.php:190
#: src/modules/author-categories/classes/AuthorCategoriesTable.php:77
msgid "Name"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:799
msgid "Name Row"
msgstr ""

#: src/core/Classes/Utils.php:1144
msgid "Need PublishPress Authors Support?"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:411
#: src/modules/author-custom-fields/author-custom-fields.php:175
#, php-format
msgid "New %1$s"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:408
#: src/modules/author-custom-fields/author-custom-fields.php:172
#, php-format
msgid "New %1s"
msgstr ""

#: src/core/Plugin.php:573
msgid "New Author"
msgstr ""

#: src/core/Classes/Term_Editor.php:42
msgid "New author added."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2654
msgid "New Author Categories Box"
msgstr ""

#: src/core/Plugin.php:572
msgid "New Author Profile"
msgstr ""

#: src/templates/parts/author-pages-grid.php:170
#: src/templates/parts/author-pages-list.php:163
msgid "Next"
msgstr ""

#: src/core/CustomFieldsModel.php:97
msgid "No"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:415
#: src/modules/author-custom-fields/author-custom-fields.php:179
#, php-format
msgid "No %2$s found"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:416
#: src/modules/author-custom-fields/author-custom-fields.php:180
#, php-format
msgid "No %2$s found in Trash"
msgstr ""

#: src/modules/author-categories/classes/AuthorCategoriesTable.php:270
msgid "No author categories."
msgstr ""

#: src/modules/author-list/classes/AuthorListTable.php:174
msgid "No author list avaliable in the selected view."
msgstr ""

#: src/core/Classes/Post_Editor.php:171
msgid "No author term"
msgstr ""

#: src/core/Plugin.php:583
msgid "No authors found."
msgstr ""

#: src/core/Classes/Author_Editor.php:965
msgid "No authors were updated"
msgstr ""

#: src/core/Plugin.php:902
msgid "No co-author exists for that term"
msgstr ""

#: src/core/Classes/Authors_Iterator.php:55
msgid ""
"No post ID provided for Authors_Iterator constructor. Are you not in a loop "
"or is $post not set?"
msgstr ""

#: src/core/WP_Cli.php:56
msgid "No posts without author terms were found"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1947
msgid "No Recent Posts by this Author"
msgstr ""

#: src/core/Authors_Widget.php:413
msgid "No recent posts from this author"
msgstr ""

#: src/core/CustomFieldsModel.php:117
msgid "nofollow"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:200
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:278
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:785
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1218
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1488
msgid "None"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:76
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:99
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:109
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:124
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:324
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:347
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:357
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:372
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:455
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:478
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:488
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:503
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:666
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:955
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:980
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:992
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1009
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1088
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1111
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1121
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1136
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1290
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1313
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1323
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1338
msgid "Normal"
msgstr ""

#: src/core/Classes/Installer.php:210
msgid "Now inspecting or updating %d total authors"
msgstr ""

#: src/core/Classes/Installer.php:317
msgid "Now inspecting or updating %d total posts"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1257
msgid "Number of comments"
msgstr ""

#: src/modules/author-categories/author-categories.php:212
#: src/modules/author-list/author-list.php:217
msgid "Number of items per page"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:111
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:359
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:490
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:994
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1123
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1325
msgid "Oblique"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:434
msgid "Open Link in New Tab"
msgstr ""

#: src/core/CustomFieldsModel.php:79
#: src/modules/author-custom-fields/author-custom-fields.php:824
msgid "Optional"
msgstr ""

#: src/modules/author-list/author-list.php:324
msgid "Options"
msgstr ""

#: src/core/Authors_Widget.php:139
msgid "Order"
msgstr ""

#: src/core/Authors_Widget.php:140
msgid "Order by"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1249
msgid "Order Recent Posts By"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:208
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1226
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1497
msgid "Outset"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:122
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:370
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:501
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1007
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1134
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1336
msgid "Overline"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:160
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:409
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:551
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:687
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:815
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1173
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1380
msgid "p"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:158
msgid "Pages"
msgstr ""

#: src/core/Plugin.php:567
msgid "Parent Author"
msgstr ""

#: src/core/Plugin.php:568
msgid "Parent Author:"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2273
msgid "Paste the editor data from the \"Export\" tab on another site."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2793
msgid ""
"Please be careful clicking these buttons. Before clicking, we recommend "
"taking a site backup in case anything goes wrong."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:4414
msgid "Please click here and read this guide!"
msgstr ""

#: src/core/Plugin.php:1578
msgid "Please complete the following required fields to save your changes:"
msgstr ""

#: src/core/Classes/Legacy/LegacyPlugin.php:284
msgid "Please correct your form errors below and try again."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:1202
msgid "Please enter the full URL to your Facebook profile."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:1220
msgid "Please enter the full URL to your Instagram page."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:1229
msgid "Please enter the full URL to your LinkedIn profile."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:1247
msgid "Please enter the full URL to your TikTok profile."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:1211
msgid "Please enter the full URL to your X profile."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:1238
msgid "Please enter the full URL to your YouTube channel."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1830
msgid "Please note this feature will not work for all themes."
msgstr ""

#: src/core/Plugin.php:1567
#: src/modules/byline-migration/byline-migration.php:148
#: src/modules/bylines-migration/bylines-migration.php:150
#: src/modules/multiple-authors/multiple-authors.php:3888
#: src/modules/multiple-authors/multiple-authors.php:3919
#: src/modules/multiple-authors/multiple-authors.php:3956
msgid "Please, wait..."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1286
msgid "Plural"
msgstr ""

#: src/modules/author-categories/author-categories.php:595
#: src/modules/author-categories/classes/AuthorCategoriesTable.php:78
#: src/modules/author-categories/classes/AuthorCategoriesTable.php:439
msgid "Plural Name"
msgstr ""

#: src/modules/author-categories/author-categories.php:259
msgid "Plural name is required."
msgstr ""

#: src/core/Widget.php:122
msgid "Plural Title"
msgstr ""

#: src/modules/polylang-integration/polylang-integration.php:56
#: src/modules/polylang-integration/polylang-integration.php:57
#: src/modules/polylang-integration/polylang-integration.php:58
msgid "Polylang Integration"
msgstr ""

#: src/core/Plugin.php:565
msgid "Popular Authors"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2181
msgid "Position"
msgstr ""

#: src/core/Widget.php:42
msgid "Post Author"
msgstr ""

#: src/core/Authors_Widget.php:191
msgid "Post Counts"
msgstr ""

#: src/core/Classes/Legacy/LegacyPlugin.php:293
msgid "Post does not exist"
msgstr ""

#: src/templates/parts/author-pages-grid.php:176
#: src/templates/parts/author-pages-list.php:169
msgid "Post not found for the author"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1592
msgid "Post preview"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:596
msgid "Post types to display on the author's profile page:"
msgstr ""

#: src/core/Plugin.php:780 src/modules/author-pages/author-pages.php:121
#: src/modules/multiple-authors/multiple-authors.php:157
msgid "Posts"
msgstr ""

#. Posts by a given author.
#: src/functions/template-tags.php:1518
#, php-format
msgid "Posts by %1$s"
msgstr ""

#: src/functions/template-tags.php:1133
#, php-format
msgid "Posts by %s"
msgstr ""

#: src/templates/parts/author-pages-grid.php:169
#: src/templates/parts/author-pages-list.php:162
msgid "Prev"
msgstr ""

#: src/modules/author-list/author-list.php:312
#: src/modules/author-list/author-list.php:482
msgid "Preview"
msgstr ""

#: src/templates/parts/author-pages-list.php:123
msgid "Published date"
msgstr ""

#: src/core/Plugin.php:782
#, php-format
msgid "Published posts of the following post types: %s"
msgstr ""

#. Author of the plugin
msgid "PublishPress"
msgstr ""

#. Name of the plugin
#: src/core/Plugin.php:715 src/modules/settings/settings.php:64
msgid "PublishPress Authors"
msgstr ""

#. Description of the plugin
#: src/modules/multiple-authors/multiple-authors.php:75
#: src/modules/multiple-authors/multiple-authors.php:79
msgid ""
"PublishPress Authors allows you to add multiple authors and guest authors to "
"WordPress posts"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:4486
msgid ""
"PublishPress Authors needs a database update for Permissions integration."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2662
msgid ""
"PublishPress Authors Pro allows you to unlock the full potential of Author "
"Categories."
msgstr ""

#: src/modules/settings/settings.php:104
msgid "PublishPress Authors Settings"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:310
msgid ""
"PublishPress Blocks has over 20 extra Gutenberg blocks including accordions, "
"galleries, tables, and more."
msgstr ""

#: src/modules/modules-settings/modules-settings.php:307
msgid ""
"PublishPress Blocks is a free plugin with full support for PublishPress "
"Authors."
msgstr ""

#: src/modules/author-categories/classes/AuthorCategoriesTable.php:431
msgid "Quick Edit"
msgstr ""

#. %s: Taxonomy term name.
#: src/modules/author-categories/classes/AuthorCategoriesTable.php:358
#, php-format
msgid "Quick edit &#8220;%s&#8221; inline"
msgstr ""

#: src/modules/author-categories/classes/AuthorCategoriesTable.php:359
msgid "Quick&nbsp;Edit"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1258
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1492
msgid "Random"
msgstr ""

#: src/modules/rank-math-seo-integration/rank-math-seo-integration.php:54
msgid "Rank Math Seo Integration"
msgstr ""

#: src/core/Classes/Utils.php:963
msgid "Read more."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1214
msgid "Recent Post Title Border Bottom Style"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1237
msgid "Recent Post Title Border Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1231
msgid "Recent Post Title Border Width"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1208
msgid "Recent Post Title Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1343
msgid "Recent Posts Alignment"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1356
msgid "Recent Posts Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1330
msgid "Recent Posts Decoration"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1368
msgid "Recent Posts HTML Tag"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1362
msgid "Recent Posts Icon Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1243
msgid "Recent Posts Limit"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1279
msgid "Recent Posts Line Height (px)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1263
msgid "Recent Posts Order"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1273
msgid "Recent Posts Size"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1318
msgid "Recent Posts Style"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1305
msgid "Recent Posts Transform"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1285
msgid "Recent Posts Weight"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:304
msgid "Recommendations for you"
msgstr ""

#: src/core/Classes/Author_Editor.php:119
#: src/core/Classes/Author_Editor.php:784
#: src/modules/author-list/author-list.php:1048
msgid "Registered Author With User Account"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:421
#: src/modules/author-custom-fields/author-custom-fields.php:185
#, php-format
msgid "Remove %1$s Image"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2435
#, php-format
msgid "Remove %1s"
msgstr ""

#: src/core/Classes/Utils.php:1133
msgid "Remove PublishPress ads and branding"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:648
msgid "Remove single author map restriction:"
msgstr ""

#: src/core/Classes/Author_Editor.php:514
#: src/core/Classes/Author_Editor.php:563
#: src/modules/multiple-authors/multiple-authors.php:2078
msgid "Remove this image"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2384
msgid "Reorder Fields"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2392
msgid ""
"Reorder the fields by dragging them to the correct position and saving your "
"changes."
msgstr ""

#: src/core/Classes/Admin_Ajax.php:311 src/core/Classes/Admin_Ajax.php:392
msgid "Request status."
msgstr ""

#: src/core/Classes/Utils.php:1151
msgid "Request Support"
msgstr ""

#: src/core/CustomFieldsModel.php:80
#: src/modules/author-custom-fields/author-custom-fields.php:826
msgid "Required"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:456
#: src/modules/author-custom-fields/author-custom-fields.php:785
msgid "Requirement"
msgstr ""

#: src/modules/rest-api/rest-api.php:58
msgid "Rest API"
msgstr ""

#: src/modules/rest-api/rest-api.php:59 src/modules/rest-api/rest-api.php:60
msgid "Rest API support"
msgstr ""

#: src/modules/author-list/classes/AuthorListTable.php:372
msgid "Restore"
msgstr ""

#: src/modules/author-categories/author-categories.php:668
#: src/modules/author-categories/author-categories.php:670
msgid "Reviewer"
msgstr ""

#: src/modules/author-categories/author-categories.php:669
msgid "Reviewers"
msgstr ""

#: src/modules/reviews/reviews.php:74
msgid "Reviews"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:206
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1224
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1495
msgid "Ridge"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:136
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:384
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:515
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1023
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1148
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1350
msgid "Right"
msgstr ""

#: src/modules/author-list/author-list.php:441
msgid "Roles"
msgstr ""

#: src/modules/author-list/author-list.php:817
#: src/modules/author-list/author-list.php:824
msgid "Save Changes"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2410
msgid "Save for All Author Boxes"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2406
msgid "Save for Current Author Box"
msgstr ""

#: src/modules/author-categories/author-categories.php:600
#: src/modules/author-custom-fields/author-custom-fields.php:418
#: src/modules/author-categories/classes/AuthorCategoriesTable.php:444
msgid "Schema Property"
msgstr ""

#: src/core/Authors_Widget.php:384 src/modules/author-list/author-list.php:328
msgid "Search"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2462
#, php-format
msgid "Search %1s Icon"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:414
#: src/modules/author-custom-fields/author-custom-fields.php:178
#, php-format
msgid "Search %2$s"
msgstr ""

#: src/modules/author-categories/author-categories.php:580
msgid "Search Author Categories"
msgstr ""

#: src/modules/author-list/author-list.php:293
msgid "Search Author Lists"
msgstr ""

#: src/core/Plugin.php:564
msgid "Search Authors"
msgstr ""

#: src/core/Authors_Widget.php:381
msgid "Search Box"
msgstr ""

#: src/core/Plugin.php:1536 src/core/Classes/Post_Editor.php:370
msgid "Search for an author"
msgstr ""

#: src/core/Classes/Post_Editor.php:480
msgid "Search for an user"
msgstr ""

#. %s: search keywords
#: src/modules/author-list/author-list.php:284
#, php-format
msgid "Search results for &#8220;%s&#8221;"
msgstr ""

#: src/core/Classes/Admin_Ajax.php:318 src/core/Classes/Admin_Ajax.php:399
#: src/modules/author-categories/author-categories.php:236
#: src/modules/author-categories/author-categories.php:319
#: src/modules/author-custom-fields/author-custom-fields.php:1068
#: src/modules/author-list/author-list.php:1237
#: src/modules/author-boxes/classes/AuthorBoxesAjax.php:41
#: src/modules/author-boxes/classes/AuthorBoxesAjax.php:89
#: src/modules/author-boxes/classes/AuthorBoxesAjax.php:131
#: src/modules/author-boxes/classes/AuthorBoxesAjax.php:188
msgid "Security error. Kindly reload this page and try again"
msgstr ""

#: src/modules/author-categories/author-categories.php:387
msgid "Security error. Kindly reload this page and try again."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2433
#, php-format
msgid "Select %1s"
msgstr ""

#: src/core/Classes/Author_Editor.php:577
msgid "Select a user"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2718
msgid "Select an author box"
msgstr ""

#: src/core/CustomFieldsModel.php:115
#: src/modules/author-list/author-list.php:348
msgid "Select an option"
msgstr ""

#: src/modules/author-list/author-list.php:438
msgid ""
"Select an option to limit the results to selected user roles, author types, "
"specific authors or author Categories."
msgstr ""

#: src/core/Classes/Author_Editor.php:829
msgid "Select Author Account"
msgstr ""

#: src/modules/author-list/author-list.php:1099
msgid "Select Author Category"
msgstr ""

#: src/modules/author-list/author-list.php:452
msgid "Select Authors to be excluded from this list."
msgstr ""

#: src/core/Classes/Author_Editor.php:510
#: src/core/Classes/Author_Editor.php:559
#: src/modules/multiple-authors/multiple-authors.php:2074
msgid "Select image"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2166
#: src/modules/multiple-authors/multiple-authors.php:2203
#: src/modules/multiple-authors/multiple-authors.php:2240
#: src/modules/multiple-authors/multiple-authors.php:2277
#: src/modules/multiple-authors/multiple-authors.php:2314
msgid "Select option"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1601
msgid "Select Preview Post"
msgstr ""

#: src/modules/author-list/author-list.php:1033
#: src/modules/author-list/author-list.php:1056
#: src/modules/multiple-authors/multiple-authors.php:1700
msgid "Select some options"
msgstr ""

#: src/core/Classes/Author_Editor.php:411
msgid ""
"Select the default Category for this Author. This will be used when the "
"Author is added to new posts."
msgstr ""

#: src/modules/author-list/author-list.php:1073
#: src/modules/author-list/author-list.php:1180
msgid "Select Users"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:592
msgid ""
"Selecting an option here will overwrite author boxes settings to match "
"selected layout."
msgstr ""

#: src/core/Plugin.php:574
msgid "Separate authors with commas"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:420
#: src/modules/author-custom-fields/author-custom-fields.php:184
#, php-format
msgid "Set %1$s Image"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:395
#: src/modules/settings/settings.php:105
msgid "Settings"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2282
#: src/modules/author-boxes/author-boxes.php:2287
msgid "Settings Imported Successfully!"
msgstr ""

#: src/modules/author-list/author-list.php:239
msgid "Settings updated successfully."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:200
#: src/modules/author-boxes/author-boxes.php:1049
#: src/modules/author-boxes/author-boxes.php:2180
#: src/modules/author-boxes/author-boxes.php:2191
#: src/modules/author-list/author-list.php:834
#: src/modules/author-list/classes/AuthorListTable.php:152
msgid "Shortcode"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1217
msgid "Shortcode documentation."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1114
#: src/modules/multiple-authors/multiple-authors.php:2986
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1606
msgid "Shortcodes"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:426
msgid "Show \"View all posts\" link"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1778
msgid ""
"Show a profile checkbox allowing Authors to be excluded from Author Boxes."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:672
msgid "Show above the content:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:792
msgid "Show After"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:655
msgid "Show after an individual author"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:800
msgid "Show author bio:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:251
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:770
msgid "Show Author Categories"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:819
msgid "Show author page title:"
msgstr ""

#: src/modules/author-list/author-list.php:437
msgid "Show Authors"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:901
msgid "Show authors:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:177
msgid "Show Avatar"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:654
msgid "Show before an individual author"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:656
msgid "Show before author group"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:680
msgid "Show below the content:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1052
msgid "Show Biographical Info"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:34
msgid "Show Box Title"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:934
msgid "Show category:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:923
msgid "Show comment counts:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:245
msgid "Show Display Name"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:721
msgid "Show email link:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1202
msgid "Show Even if No Recent Post"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:890
msgid "Show excerpt:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:857
msgid "Show featured image:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:912
msgid "Show post date:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:956
msgid "Show read more link:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1190
msgid "Show Recent Posts"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1196
msgid "Show Recent Posts Title"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:729
msgid "Show site link:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:945
msgid "Show tags:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:615
msgid "Show username in the search field:"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:305
msgid "Showcase your Authors with PublishPress Blocks"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:33
msgid "Simple List"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1119
msgid "Simple list legacy layout Author Box:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:603
msgid "Simple Name Authors Block (Categories)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:604
msgid "Simple Name Authors Inline (Categories)"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1279
msgid "Single"
msgstr ""

#: src/core/Widget.php:121
msgid "Single Title"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:403
#, php-format
msgctxt "singular author box post type name"
msgid "%1$s"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:167
#, php-format
msgctxt "singular custom field post type name"
msgid "%1$s"
msgstr ""

#: src/modules/author-categories/author-categories.php:590
#: src/modules/author-categories/classes/AuthorCategoriesTable.php:434
msgid "Singular Name"
msgstr ""

#: src/modules/author-categories/author-categories.php:254
msgid "Singular name is required."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:930
msgid "Size"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:787
#: src/modules/author-categories/classes/AuthorCategoriesTable.php:79
msgid "Slug"
msgstr ""

#: src/modules/author-list/author-list.php:365
msgid "Small-sized image (150px by 150px)"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:405
msgid "Social Profile?"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:203
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1221
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1491
msgid "Solid"
msgstr ""

#: src/core/Plugin.php:1568
msgid "Sorry, the request returned an error."
msgstr ""

#: src/core/Classes/Admin_Ajax.php:335 src/core/Classes/Author_Editor.php:1009
msgid ""
"Sorry, this WordPress user is already mapped to another Author. By default, "
"each user can only be connected to one Author profile."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:159
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:408
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:550
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:686
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:812
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1172
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1379
msgid "span"
msgstr ""

#: src/modules/author-list/author-list.php:842
msgid "Static Shortcode"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:784
msgid "Status"
msgstr ""

#: src/core/Classes/Utils.php:1132
msgid "Support for Polylang"
msgstr ""

#: src/core/Classes/Author_Editor.php:892
#: src/modules/multiple-authors/multiple-authors.php:3884
msgid "Sync author and user fields"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3915
msgid "Sync author and user URLs"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2743
msgid "Synchronize PublishPress Authors Fields and user profile fields"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2750
msgid "Synchronize the author and user URLs"
msgstr ""

#: src/core/Plugin.php:554
msgctxt "taxonomy general name"
msgid "Authors"
msgstr ""

#: src/core/Plugin.php:559
msgctxt "taxonomy singular name"
msgid "Author"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2342
msgid "Template generated successfuly!"
msgstr ""

#: src/core/CustomFieldsModel.php:40
msgid "Text"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1396
#, php-format
msgid ""
"The authors data shortcode accepts field parameter such as: %1$s %2$s %3$s "
"%4$s %5$s %6$s %7$s. You can see full details and parameters %8$s in this "
"guide %9$s."
msgstr ""

#: src/modules/author-list/author-list.php:361
msgid "The original size of the uploaded image"
msgstr ""

#: src/modules/seoframework-integration/seoframework-integration.php:59
msgid "The SEO Framework Integration"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:387
msgid ""
"The slug is used in code to reference this author field. It is all lowercase "
"and contains only letters, numbers, and hyphens."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:741
msgid "Theme"
msgstr ""

#: src/modules/settings/settings.php:204
msgid "There are no PublishPress modules registered"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1368
#, php-format
msgid ""
"There is one final option to mention. This is mostly useful if you're using "
"a theme or page builder to customize the Author profile pages you find at "
"URLs such as /author/username/. You can use the following shortcode on the "
"authors page to display the profile of the current author. You just need to "
"add the parameter %s."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2786
msgid ""
"This action can reset the PublishPress Authors data before using other "
"maintenance options. Guest authors are author profiles that are not mapped "
"to a WordPress user account. This action will delete all guest authors."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2779
msgid ""
"This action can reset the PublishPress Authors data before using other "
"maintenance options. It will delete all author profiles that are mapped to a "
"WordPress user account. This will not delete the WordPress user accounts, "
"but any links between the posts and multiple authors will be lost."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2771
msgid ""
"This action will copy the authors from the plugin Co-Authors Plus allowing "
"you to migrate to PublishPress Authors without losing any data. This action "
"can be run multiple times."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2087
msgid ""
"This avatar will be used as default avatar instead of gravatar where no "
"custom avatar is added to profile."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:466
msgid ""
"This description appears under the fields and helps users understand their "
"choice."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:409
msgid ""
"This feature will add the SameAs property to this link so that search "
"engines realize that the social profile is connected to this author."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:437
msgid "This feature will add the target=”_blank” attribute to your link."
msgstr ""

#: src/core/Plugin.php:528
msgid ""
"This forms part of the URL for the author’s profile page. If you choose a "
"Mapped User, this URL is taken from the user’s account and can not be "
"changed."
msgstr ""

#: src/modules/author-list/author-list.php:1137
msgid ""
"This is a quick preview of this Author List. Test on frontend pages to see "
"exactly how it looks with your theme."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2178
#: src/modules/multiple-authors/multiple-authors.php:2215
#: src/modules/multiple-authors/multiple-authors.php:2252
#: src/modules/multiple-authors/multiple-authors.php:2289
#: src/modules/multiple-authors/multiple-authors.php:2326
#, php-format
msgid "This is useful if you use legacy shortcode %s"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:828
msgid ""
"This is useful when linking to an email, URL, or phone number. For example, "
"'mailto:', 'https://' or 'tel:' can be added as the prefix."
msgstr ""

#: src/core/Plugin.php:523
msgid ""
"This name is used in several default displays and some search engine "
"integrations."
msgstr ""

#: src/core/Classes/Post_Editor.php:472
msgid ""
"This option is showing because you do not have a WordPress user selected as "
"an author. For some tasks, it can be helpful to have a user selected here. "
"This user will not be visible on the front of your site."
msgstr ""

#: publishpress-authors.php:93
msgid "This plugin can be deleted."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2600
msgid "This setting may be disabled for users who can not edit others posts."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2418
msgid ""
"This will allows you map a WordPress user to more than one author. Don't use "
"this feature unless requested to do so by the PublishPress team. This plugin "
"works better when authors and users are paired one-to-one."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2758
msgid ""
"This will create author categories table if missing and add default "
"categories."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1399
msgid ""
"This will display author in an inline format side by side instead of block "
"format."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2370
msgid "This will display author page title."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2109
msgid "This will display the author bio."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1254
msgid "This will display the authors box at the end of the content."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1233
msgid "This will display the authors box at the top of the content."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2654
msgid "This will display the authors email in the author box."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2674
msgid "This will display the authors site in the author box."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2442
msgid "This will display the authors."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2514
msgid "This will display the categories."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2490
msgid "This will display the comment count."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2394
msgid "This will display the excerpt."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2346
msgid "This will display the featured image."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2466
msgid "This will display the published date."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2562
msgid "This will display the read more link."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2538
msgid "This will display the tags."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2738
msgid ""
"This will find all the users in a role and create author profiles for them. "
"You can choose the roles using the \"Automatically create author profiles\" "
"setting."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2694
msgid "This will load Font Awesome icons for use in Author Boxes."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2712
msgid "This will remove the Authors Box in \"Quick Edit\"."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2744
msgid ""
"This will update all the author profiles in PublishPress Authors to match "
"the default WordPress fields for each user."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2751
msgid ""
"This will update all the Author URLs in PublishPress Authors to match the "
"default WordPress URLs for each user."
msgstr ""

#: src/modules/rest-api/rest-api.php:313
msgid "This WordPress user is already mapped to another author."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:1242
msgid "TikTok"
msgstr ""

#: src/core/Authors_Widget.php:130 src/modules/author-list/author-list.php:384
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1255
#: src/modules/author-list/classes/AuthorListTable.php:150
msgid "Title"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:704
msgid "Title for the author box:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1569
#, php-format
msgid "To display a search box for authors, use %1$s ."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1558
#, php-format
msgid "To further customize the order of results, use %1$s or %2$s ."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1545
#, php-format
msgid ""
"To order the results based on post count, use %1$s . To order the results by "
"name, use %2$s . Alternatively, you can order by profile fields like %3$s, "
"%4$s etc"
msgstr ""

#: src/modules/author-list/classes/AuthorListTable.php:356
msgid "Trash"
msgstr ""

#: src/modules/author-list/classes/AuthorListTable.php:65
#, php-format
msgid "Trash %s"
msgid_plural "Trash %s"
msgstr[0] ""
msgstr[1] ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:600
msgid "Two Columns (Categories)"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:786
msgid "Type"
msgstr ""

#: src/modules/ultimatemember-integration/ultimatemember-integration.php:54
msgid "Ultimate Member Integration"
msgstr ""

#: src/modules/ultimate-post-integration/ultimate-post-integration.php:53
msgid "Ultimate_Post Integration"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:121
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:369
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:500
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1006
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1133
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1335
msgid "Underline"
msgstr ""

#: src/modules/author-categories/classes/AuthorCategoriesTable.php:458
msgid "Update"
msgstr ""

#: src/core/Plugin.php:571
msgid "Update Author"
msgstr ""

#: src/core/Classes/Author_Editor.php:900
msgid "Update post count"
msgstr ""

#: src/core/Classes/Author_Editor.php:968
msgid "Updated %d authors"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3921
msgid "Updated %d of %d authors..."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3890
msgid "Updated %d of %d posts..."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3889
msgid "Updating author field on posts..."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3920
msgid "Updating authors slug..."
msgstr ""

#: includes.php:63 src/core/Classes/Utils.php:1137
#: src/modules/author-boxes/author-boxes.php:2667
#: src/modules/author-custom-fields/author-custom-fields.php:976
#: src/modules/author-custom-fields/author-custom-fields.php:1029
#: src/modules/author-list/author-list.php:1160
msgid "Upgrade to Pro"
msgstr ""

#: src/core/Classes/Utils.php:1120
msgid "Upgrade to PublishPress Authors Pro"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:96
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:344
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:475
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:977
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1108
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1310
msgid "Uppercase"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:422
#: src/modules/author-custom-fields/author-custom-fields.php:186
#, php-format
msgid "Use as %1$s Image"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:309
msgid ""
"Use the Content Display block to show your posts in many beautiful layouts."
msgstr ""

#: src/modules/author-list/author-list.php:320
msgid "Users"
msgstr ""

#: src/core/Authors_Widget.php:183
msgid "Users Authors"
msgstr ""

#: src/core/Plugin.php:1575
msgid "View"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:413
#: src/modules/author-custom-fields/author-custom-fields.php:177
#, php-format
msgid "View %1$s"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1102
msgid "View All Posts"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:589
#: src/modules/author-boxes/author-boxes.php:627
#: src/modules/default-layouts/default-layouts.php:107
#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:99
#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:257
#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:370
#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:448
#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:532
#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:620
msgid "View all posts"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:432
msgid "View All Posts Label"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:508
msgid "View All Posts Link Alignment"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:527
msgid "View All Posts Link Background Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:495
msgid "View All Posts Link Decoration"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:533
msgid "View All Posts Link Hover Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:539
msgid "View All Posts Link HTML Tag"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:444
msgid "View All Posts Link Line Height (px)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:521
msgid "View All Posts Link Meta Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:438
msgid "View All Posts Link Size"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:483
msgid "View All Posts Link Style"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:470
msgid "View All Posts Link Transform"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:450
msgid "View All Posts Link Weight"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:798
msgid "View all posts Row"
msgstr ""

#: src/core/Plugin.php:570
msgid "View Author"
msgstr ""

#: src/core/Classes/Utils.php:1162
msgid "View Knowledge Base"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1843
msgid "View sample Author Page"
msgstr ""

#: src/core/Classes/Author_Editor.php:404
#: src/modules/author-custom-fields/author-custom-fields.php:939
msgid "Website"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:973
#: src/modules/author-custom-fields/author-custom-fields.php:1028
msgid ""
"With PublishPress Authors Pro, you can add new fields for social networks "
"and more."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1307
msgid ""
"With this shortcode you can display the author box in any part of the "
"content. "
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1388
msgid ""
"With this shortcode you can display the author names or any profile field in "
"any part of the content."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1457
msgid ""
"With this shortcode, you can show all the authors together in a single "
"display."
msgstr ""

#: src/modules/wpengine-integration/wpengine-integration.php:52
msgid "WPEngine Integration"
msgstr ""

#: src/core/CustomFieldsModel.php:42
msgid "WYSIWYG Editor"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:1206
msgid "X"
msgstr ""

#: src/core/CustomFieldsModel.php:98
msgid "Yes, this is a Social Profile"
msgstr ""

#: src/modules/yoast-seo-integration/yoast-seo-integration.php:53
msgid "Yoast SEO Integration"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1435
#, php-format
msgid ""
"You can also decide to return an array list of authors by using "
"user_objects=\"true\" which will return all the authors object data as an "
"array. You can check full details and sample usage %2$s in this guide%3$s."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1326
#, php-format
msgid ""
"You can also decide whether or not to show the main title, using %1$s or "
"%2$s ."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1579
#, php-format
msgid ""
"You can also show a dropdown menu that allows users to search on specific "
"author fields. You can add fields to the dropdown using %1$s . This requires "
"the search box to be active."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1413
msgid ""
"You can also specify the separator to be used for mulitple authors data."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1490
#, php-format
msgid ""
"You can choose the number of authors per page using %1$s . %2$s Pagination "
"will be automatically added if required."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2664
msgid ""
"You can create Author Boxes where Authors are grouped into their categories."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1480
#, php-format
msgid ""
"You can define the number of layout columns by using %1$s to show authors in "
"2 column."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1589
#, php-format
msgid ""
"You can limit the author list to users with a published post within a "
"specific period using %1$s . This accept english date value like 1 week ago, "
"1 month ago, 6 months ago, 1 year ago etc."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1501
#, php-format
msgid ""
"You can limit the result to only authors who are assigned to posts by using "
"%1$s . %2$s Alternatively, use %3$s to show all authors, including those "
"without any posts."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1513
#, php-format
msgid ""
"You can limit the result to only guest authors by using %1$s . %2$s "
"Alternatively, %3$s will show only authors with a WordPress account."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1377
msgid ""
"You can load the authors belonging to a specific author categories by "
"providing the category slug or slugs separated by comma for more than one "
"category."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1350
msgid ""
"You can load the authors for a specific author by providing the author term "
"id for both guest and user author. For example, this shortcode will load the "
"author with term id of 32"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1336
#: src/modules/multiple-authors/multiple-authors.php:1420
msgid ""
"You can load the authors for a specific post, even if you are not in that "
"post currently. For example, this shortcode will load the authors for the "
"post with the ID of 32."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1343
msgid ""
"You can load the authors for a specific user by providing the user id. For "
"example, this shortcode will load the author with user id of 32"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1427
msgid ""
"You can retrieve a specific author by providing the author's term ID. For "
"example, this shortcode will load the author with the term ID of 102."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1446
msgid ""
"You can retrieve authors belonging to a specific Author Category by "
"providing the category slug or slugs. Separate multiple slugs with a comma."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1423
msgid ""
"You can specify a separator such as ',' to separate authors. This field "
"accepts basic HTML."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1315
#, php-format
msgid ""
"You can specify layout by using author boxes layout slug. You can see full "
"details of each layout option %1$s in this guide %2$s."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1465
#, php-format
msgid ""
"You can specify layout by using author boxes layout slug. You can see full "
"details of each layout option %1$s in this guide %2$s. %3$s %4$s This "
"shortcode also provides two custom layouts: %5$s %6$s."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2248
#, php-format
msgid "You can use basic HTML in this field. For example: Read Time %1s."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:887
#, php-format
msgid ""
"You can use icons from Dashicons and Font Awesome. %1s %2sClick here for "
"documentation%3s."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1587
msgid "You can use multiple class names. Leave a space between each class."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:2253
msgid ""
"You can use this data to export your author box design and import it to a "
"new site."
msgstr ""

#: src/modules/rest-api/rest-api.php:464
msgid "You cannot edit author mapped to administrator account."
msgstr ""

#: src/core/Classes/Legacy/LegacyPlugin.php:289
msgid "You do not have necessary permissions to complete this action."
msgstr ""

#: src/modules/rest-api/rest-api.php:474
msgid "You do not have permission to edit this author."
msgstr ""

#: src/modules/author-categories/author-categories.php:241
#: src/modules/author-categories/author-categories.php:324
#: src/modules/author-custom-fields/author-custom-fields.php:1074
#: src/modules/author-boxes/classes/AuthorBoxesAjax.php:47
msgid "You do not have permission to perform this action"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:577
msgid "You need to enable atleast one author category to use this feature."
msgstr ""

#: includes.php:39
#, php-format
msgid ""
"You're using PublishPress Authors Free. The Pro version has more features "
"and support. %sUpgrade to Pro%s"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:1233
msgid "YouTube"
msgstr ""
