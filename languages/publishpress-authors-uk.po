# Copyright (C) 2012 PublishPress Authors
# This file is distributed under the same license as the PublishPress Authors package.
msgid ""
msgstr ""
"Project-Id-Version: PublishPress Authors\n"
"Report-Msgid-Bugs-To: http://wordpress.org/tag/publishpress-multiple-"
"authors\n"
"POT-Creation-Date: 2021-11-04 15:04-0300\n"
"PO-Revision-Date: 2023-01-31 15:25+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Ukrainian\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-SearchPath-0: .\n"

#: src/functions/template-tags.php:618 src/functions/template-tags.php:1239
msgid " and "
msgstr ""

#: src/core/Classes/Post_Editor.php:168
msgid "\"post_author\" is empty"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:337
#: src/modules/author-custom-fields/author-custom-fields.php:155
#, php-format
msgid "%1$s Image"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:582
#, php-format
msgid "%1s Display"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:633
#, php-format
msgid "%1s Display Icon"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:651
#, php-format
msgid "%1s Display Icon Background Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:659
#, php-format
msgid "%1s Display Icon Border Radius %2s"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:642
#, php-format
msgid "%1s Display Icon Size"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:598
#, php-format
msgid "%1s Display Prefix"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:606
#, php-format
msgid "%1s Display Suffix"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:573
#, php-format
msgid "%1s Value Prefix"
msgstr ""

#: src/functions/template-tags.php:477
#, php-format
msgid "%1sView all posts%2s by %3s"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:330
#: src/modules/author-custom-fields/author-custom-fields.php:148
#, php-format
msgid "%2$s"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:343
#: src/modules/author-custom-fields/author-custom-fields.php:161
#, php-format
msgid "%2$s list"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:342
#: src/modules/author-custom-fields/author-custom-fields.php:160
#, php-format
msgid "%2$s list navigation"
msgstr ""

#: src/core/WP_Cli.php:71
#, php-format
msgid "%d posts were found without author terms"
msgstr ""

#: src/core/Classes/Installer.php:306
#, php-format
msgid "%d/%d: Inspecting the post %d"
msgstr ""

#: src/core/Classes/Installer.php:201
#, php-format
msgid "%d/%d: Inspecting the user %d"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:553
msgid ""
"'span' will display as an inline element and 'div' will display as a block "
"element. To make this display into a link, select 'link' and enter the first "
"part of the URL into the 'Prefix' field."
msgstr ""

#: src/core/Classes/Author_Editor.php:373
#, php-format
msgid "(Uses the %1s Email field %2s to find the Gravatar account)"
msgstr ""

#: src/functions/template-tags.php:1240
msgid ", and "
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:78
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:263
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:405
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:695
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:821
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1023
msgid "100 - Thin"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:79
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:264
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:406
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:696
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:822
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1024
msgid "200 - Extra light"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:80
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:265
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:407
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:697
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:823
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1025
msgid "300 - Light"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:81
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:266
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:408
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:698
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:824
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1026
msgid "400 - Normal"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:82
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:267
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:409
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:699
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:825
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1027
msgid "500 - Medium"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:83
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:268
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:410
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:700
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:826
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1028
msgid "600 - Semi bold"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:84
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:269
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:411
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:701
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:827
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1029
msgid "700 - Bold"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:85
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:270
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:412
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:702
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:828
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1030
msgid "800 - Extra bold"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:86
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:271
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:413
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:703
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:829
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1031
msgid "900 - Black"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3067
msgid "Access denied"
msgstr ""

#: src/core/CustomFieldsModel.php:61
#: src/modules/author-custom-fields/author-custom-fields.php:664
msgid "Active"
msgstr ""

#: src/modules/divi-integration/divi-integration.php:53
msgid "Add compatibility with the Divi Theme Builder"
msgstr ""

#: src/modules/editflow-integration/editflow-integration.php:52
msgid "Add compatibility with the Edit Flow plugin"
msgstr ""

#: src/modules/elementor-integration/elementor-integration.php:58
msgid "Add compatibility with the Elementor and Elementor Pro page builder"
msgstr ""

#: src/modules/generatepress-integration/generatepress-integration.php:53
msgid "Add compatibility with the Generatepress theme"
msgstr ""

#: src/modules/genesis-integration/genesis-integration.php:53
msgid "Add compatibility with the Genesis framework"
msgstr ""

#: src/modules/rank-math-seo-integration/rank-math-seo-integration.php:54
msgid "Add compatibility with the Rank Math Seo plugin"
msgstr ""

#: src/modules/seoframework-integration/seoframework-integration.php:59
msgid "Add compatibility with The SEO Framework plugin."
msgstr ""

#: src/modules/ultimatemember-integration/ultimatemember-integration.php:54
msgid "Add compatibility with the Ultimate Member plugin"
msgstr ""

#: src/modules/wpengine-integration/wpengine-integration.php:52
msgid "Add compatibility with the WPEngine object cache."
msgstr ""

#: src/modules/yoast-seo-integration/yoast-seo-integration.php:53
msgid "Add compatibility with the Yoast SEO plugin"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1276
msgid "Add Custom CSS styles here..."
msgstr ""

#: src/modules/debug/debug.php:54
msgid "Add debug information for the plugin"
msgstr ""

#: src/core/Classes/Utils.php:1070
msgid "Add fields for social networks"
msgstr ""

#: src/modules/byline-migration/byline-migration.php:73
#: src/modules/byline-migration/byline-migration.php:74
msgid "Add migration option for Byline"
msgstr ""

#: src/modules/bylines-migration/bylines-migration.php:76
#: src/modules/bylines-migration/bylines-migration.php:77
msgid "Add migration option for Bylines"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:327
#: src/modules/author-custom-fields/author-custom-fields.php:145
#, php-format
msgid "Add New %1$s"
msgstr ""

#: src/core/Classes/Utils.php:1069
msgid "Add new custom fields"
msgstr ""

#: src/core/Plugin.php:564
msgid "Add or remove authors"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:83
#: src/modules/author-boxes/author-boxes.php:87
msgid "Add support for author boxes."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:64
#: src/modules/author-custom-fields/author-custom-fields.php:68
msgid "Add support for custom fields in the author profiles."
msgstr ""

#: src/core/Classes/Installer.php:333
#, php-format
msgid "Adding the author term %d to the post %d"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2477
msgid "Advanced"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:622
#, php-format
msgid "After %1s Display Suffix"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:129
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:314
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:456
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:752
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:872
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1074
msgid "Alignment"
msgstr ""

#: src/core/Plugin.php:552 src/core/Authors_Widget.php:178
#: src/core/Authors_Widget.php:374 src/core/Classes/Post_Editor.php:392
#, fuzzy
#| msgid "All authors"
msgid "All Authors"
msgstr "Всі гостьові автори"

#: src/core/Classes/Installer.php:213
msgid "All is set. No author need to be updated"
msgstr ""

#: src/core/Classes/Installer.php:352
msgid "All is set. No posts need to be updated"
msgstr ""

#: src/core/Classes/Author_Editor.php:854
msgid "An author with the name provided already exists."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesAjax.php:33
#: src/modules/author-boxes/classes/AuthorBoxesAjax.php:96
msgid "An error occured."
msgstr ""

#: src/core/Classes/Admin_Ajax.php:309
msgid "Another user with Author URL already exists."
msgstr ""

#: src/core/Plugin.php:1466
msgid ""
"Are you sure you want to create author profiles for the missed post authors?"
msgstr ""

#: src/core/Plugin.php:1478
msgid "Are you sure you want to create authors for the selected roles?"
msgstr ""

#: src/core/Plugin.php:1458
msgid ""
"Are you sure you want to delete the authors profiles mapped to users? This "
"action can't be undone."
msgstr ""

#: src/core/Plugin.php:1462
msgid ""
"Are you sure you want to delete the guest authors profiles? This action "
"can't be undone."
msgstr ""

#: src/core/Plugin.php:1445
msgid "Are you sure you want to remove this author?"
msgstr ""

#: src/core/Plugin.php:1470
msgid "Are you sure you want to update the author column for all the posts?"
msgstr ""

#: src/core/Plugin.php:1474
msgid "Are you sure you want to update the author slug for all the users?"
msgstr ""

#: src/core/Authors_Widget.php:183
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:998
msgid "Ascending"
msgstr ""

#: src/core/Plugin.php:570
#: src/modules/multiple-authors/multiple-authors.php:1104
msgid "Author"
msgstr ""

#: src/core/Classes/Author_Editor.php:344
msgid "Author Bio"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:776
msgid "Author bio layout:"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:316
#: src/modules/author-boxes/author-boxes.php:418
msgid "Author Box"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:394
msgid "Author Box added."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:725
msgid "Author Box Editor"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:320
#, php-format
msgctxt "Author Box post type name"
msgid "%2$s"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:708
msgid "Author Box Preview"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:395
msgid "Author Box saved."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:396
msgid "Author Box submitted."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:392
#: src/modules/author-boxes/author-boxes.php:393
msgid "Author Box updated."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2473
#: src/modules/author-boxes/author-boxes.php:82
#: src/modules/author-boxes/author-boxes.php:317
#: src/modules/author-boxes/author-boxes.php:375
#: src/modules/author-boxes/author-boxes.php:376
#: src/modules/author-boxes/author-boxes.php:419
msgid "Author Boxes"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:134
#, fuzzy
#| msgid "New Author Field"
msgid "Author Field"
msgstr "Новий гостьовий автор"

#: src/modules/author-boxes/author-boxes.php:815
#: src/modules/author-custom-fields/author-custom-fields.php:63
#: src/modules/author-custom-fields/author-custom-fields.php:135
#: src/modules/author-custom-fields/author-custom-fields.php:193
#: src/modules/author-custom-fields/author-custom-fields.php:194
#, fuzzy
#| msgid "Authors"
msgid "Author Fields"
msgstr "Автори"

#: src/core/Classes/Admin_Ajax.php:342
msgid "Author name is required"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2474
msgid "Author Pages"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:795
msgid "Author pages excerpt ellipsis:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:743
msgid "Author pages layout:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:732
msgid "Author pages posts limit:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:806
msgid "Author pages title header:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:814
msgid "Author post title header:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:318
#: src/modules/multiple-authors/multiple-authors.php:319
msgid "Author Profile"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1528
msgid ""
"Author profiles can be mapped to WordPress user accounts. This option allows "
"you to automatically create author profiles when users are created in these "
"roles. You can also do this for existing users by clicking the \"Create "
"missed authors from role\" button in the Maintenance tab."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:819
msgid "Author Recent Posts"
msgstr ""

#: src/core/Classes/Term_Editor.php:56
#: src/modules/multiple-authors/multiple-authors.php:2565
#, fuzzy
#| msgid "Authors"
msgid "Author URL"
msgstr "Автори"

#: src/core/Classes/Admin_Ajax.php:296
msgid "Author URL cannot be empty."
msgstr ""

#: src/core/Authors_Widget.php:141
msgid "Author's search box field (Seperate multiple fields by comma(','))"
msgstr ""

#. Author archive title. 1: Author name
#: src/core/Classes/Integrations/Theme.php:40
#, fuzzy, php-format
#| msgid "Authors"
msgid "Author: %s"
msgstr "Автори"

#: src/core/Plugin.php:840 src/core/Authors_Widget.php:138
#: src/core/Traits/Author_box.php:166 src/core/Classes/Post_Editor.php:93
#: src/core/Classes/Post_Editor.php:196
#: src/modules/multiple-authors/multiple-authors.php:303
#: src/modules/multiple-authors/multiple-authors.php:304
#: src/modules/multiple-authors/multiple-authors.php:352
#: src/modules/multiple-authors/multiple-authors.php:353
#: src/modules/multiple-authors/multiple-authors.php:372
#: src/modules/multiple-authors/multiple-authors.php:1110
#: src/modules/editflow-integration/editflow-integration.php:113
#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:79
#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:133
#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:187
#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:254
#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:326
msgid "Authors"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1141
msgid "Authors Box"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1206
msgid "Authors Data"
msgstr ""

#: src/modules/debug/debug.php:97
#, fuzzy
#| msgid "Authors"
msgid "Authors Debug"
msgstr "Автори"

#: src/modules/default-layouts/default-layouts.php:176
msgid "Authors index (Legacy)"
msgstr ""

#: src/core/Authors_Widget.php:23
#: src/modules/multiple-authors/multiple-authors.php:1268
#, fuzzy
#| msgid "Authors"
msgid "Authors List"
msgstr "Автори"

#: src/modules/default-layouts/default-layouts.php:177
msgid "Authors recent (Legacy)"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:54
msgid "Authors Settings"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:566
msgid "Automatically create author profiles:"
msgstr ""

#: src/core/Classes/Author_Editor.php:312
#: src/modules/author-boxes/author-boxes.php:799
msgid "Avatar"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:183
msgid "Avatar size (px)"
msgstr ""

#: src/core/Classes/Author_Editor.php:365
msgid "Avatar Source"
msgstr ""

#: src/core/Plugin.php:571
#, fuzzy
#| msgid "Search Guest Authors"
msgid "Back to Authors"
msgstr "Шукати гостьових авторів"

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1221
msgid "Background Color"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:775
#: src/modules/author-custom-fields/author-custom-fields.php:605
msgid "Banner"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:614
#, php-format
msgid "Before %1s Display Prefix"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:807
msgid "Biographical Info"
msgstr "Опис"

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:547
msgid "Biographical Info Row"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:77
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:262
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:404
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:694
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:820
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1022
msgid "Bold"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:212
msgid "Border Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1200
msgid "Border color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:218
msgid "Border Radius (%)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1215
msgid "Border radius (px)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:189
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1182
msgid "Border Style"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:206
msgid "Border Width"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1176
msgid "Border Width (px)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:53
msgid "Bottom space"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:823
msgid "Box Layout"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1206
msgid "Box Width (%)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:28
msgid "Boxed"
msgstr ""

#: src/modules/default-layouts/default-layouts.php:171
msgid "Boxed (Legacy)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:98
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:283
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:425
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:717
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:841
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1043
msgid "Capitalize"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:135
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:320
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:462
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:760
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:878
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1080
msgid "Center"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:29
msgid "Centered"
msgstr ""

#: src/modules/default-layouts/default-layouts.php:172
msgid "Centered (Legacy)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:795
msgid "Character Limit"
msgstr ""

#: src/core/Classes/Legacy/LegacyPlugin.php:276
msgid "Cheatin&#8217; uh?"
msgstr ""

#: src/core/Plugin.php:565
msgid "Choose from the most used Authors"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:292
msgid "Click here to install PublishPress Blocks"
msgstr ""

#: src/core/Plugin.php:843
msgid "Click on an author to change them. Drag to change their order."
msgstr ""

#: src/core/Plugin.php:1454
msgid ""
"Click on an author to change them. Drag to change their order. Click on "
"<strong>Remove</strong> to remove them."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1574
msgid ""
"Click the \"Generate Template\" button under the text area. Wait for the "
"code to be generated."
msgstr ""

#: src/core/Plugin.php:1449
msgid "Click to change this author, or drag to change their position"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1454
#: src/modules/multiple-authors/multiple-authors.php:1455
msgid "Click To Copy!"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3960
msgid "Click to run the update now"
msgstr ""

#: src/core/Classes/Utils.php:74
msgid "Co-Authors Plus must be installed and active."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:142
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:327
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:767
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:885
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1087
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1227
msgid "Color"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:677
msgid "Color scheme:"
msgstr ""

#: src/templates/parts/author-pages-list.php:123
#: src/templates/parts/author-pages-grid.php:134
msgid "Comment counts"
msgstr ""

#: src/core/Classes/Legacy/LegacyPlugin.php:269
msgid "Configure"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1577
msgid ""
"Congratulations. Your can now choose your template inside the PublishPress "
"Authors Settings."
msgstr ""

#: src/core/Classes/Author_Editor.php:716
#, fuzzy
#| msgid "About the guest author"
msgid "Convert into guest author"
msgstr "Про гостьового автора"

#: src/modules/author-boxes/author-boxes.php:1546
#: src/modules/author-boxes/author-boxes.php:1611
msgid "Copied to Clipboard!"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1453
msgid "Copied!"
msgstr ""

#: src/modules/byline-migration/byline-migration.php:158
msgid "Copy Byline Data"
msgstr ""

#: src/modules/bylines-migration/bylines-migration.php:160
msgid "Copy Bylines Data"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2272
msgid "Copy Co-Authors Plus Data"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1576
msgid "Copy the generated code and paste it inside the newly created file."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1542
#: src/modules/author-boxes/author-boxes.php:1606
msgid "Copy to Clipboard"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1575
#, php-format
msgid ""
"Create an empty php template file with your desired file slug in the %1s "
"/publishpress-authors/author-boxes/ %2s folder of your theme. %3s For "
"example, the file can be located here: %4s /wp-content/themes/%5syour-theme-"
"name%6s/publishpress-authors/author-boxes/my-first-custom-author-template."
"php %7s ."
msgstr ""

#: src/core/Classes/Author_Editor.php:171
#, fuzzy
#| msgid "Create Profile"
msgid "Create Author Profile"
msgstr "Створити обліковий запис"

#: src/modules/multiple-authors/multiple-authors.php:2259
#: src/modules/multiple-authors/multiple-authors.php:2261
msgid "Create default Author Boxes"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2239
#: src/modules/multiple-authors/multiple-authors.php:2241
msgid "Create missed authors from role"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2233
#: src/modules/multiple-authors/multiple-authors.php:2235
msgid "Create missed post authors"
msgstr ""

#: src/core/Classes/Installer.php:320
#, php-format
msgid "Creating author term for the user %d"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:827
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1275
msgid "Custom CSS"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:236
msgid "Custom Field"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:154
#, php-format
msgctxt "custom field post type menu name"
msgid "%2$s"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:138
#, php-format
msgctxt "Custom Field post type name"
msgid "%2$s"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:212
msgid "Custom Field published."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:213
msgid "Custom Field saved."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:214
msgid "Custom Field submitted."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:210
#: src/modules/author-custom-fields/author-custom-fields.php:211
msgid "Custom Field updated."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:237
msgid "Custom Fields"
msgstr ""

#: src/core/Classes/Author_Editor.php:381
msgid "Custom image"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:336
#, php-format
msgctxt "custom layout post type menu name"
msgid "%2$s"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:195
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:951
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1188
msgid "Dashed"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:984
msgid "Date"
msgstr ""

#: src/modules/debug/debug.php:53
msgid "Debug"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:116
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:301
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:443
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:737
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:859
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1061
msgid "Decoration"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:75
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:95
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:108
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:120
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:133
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:260
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:280
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:293
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:305
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:318
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:402
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:422
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:435
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:447
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:460
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:692
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:714
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:729
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:743
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:758
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:818
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:838
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:851
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:863
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:876
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1020
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1040
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1053
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1065
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1078
msgid "Default"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:599
msgid "Default author for new posts:"
msgstr ""

#: src/modules/default-layouts/default-layouts.php:54
msgid "Default Layouts"
msgstr ""

#: src/core/Authors_Widget.php:358
msgid "Default Search"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2282
msgid "Delete all authors mapped to users"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2289
#, fuzzy
#| msgid "All Guest Authors"
msgid "Delete all guest authors"
msgstr "Всі гостьові автори"

#: src/modules/multiple-authors/multiple-authors.php:2287
#, fuzzy
#| msgid "All Guest Authors"
msgid "Delete Guest Authors"
msgstr "Всі гостьові автори"

#: src/modules/multiple-authors/multiple-authors.php:2280
msgid "Delete Mapped Authors"
msgstr ""

#: src/core/Authors_Widget.php:184
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:999
msgid "Descending"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:356
msgid "Description"
msgstr ""

#: src/core/Classes/Utils.php:1098
msgid "Detailed documentation is also available on the plugin website."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:294
msgid "Details"
msgstr ""

#: src/core/Classes/Post_Editor.php:334
msgid "Disable post author box display?"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:653
msgid "Disable the \"Authors\" box when using \"Quick Edit\":"
msgstr ""

#: src/core/CustomFieldsModel.php:62
#: src/modules/author-custom-fields/author-custom-fields.php:668
msgid "Disabled"
msgstr ""

#: src/modules/settings/settings.php:271
#: src/modules/multiple-authors/multiple-authors.php:1068
#, php-format
msgid ""
"Disabled because add_post_type_support('%1$s', '%2$s') is included in a "
"loaded file."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3892
#: src/modules/multiple-authors/multiple-authors.php:3966
msgid "Dismiss"
msgstr ""

#: src/core/Widget.php:48
msgid "Display a list of authors for the current post."
msgstr ""

#: src/core/Authors_Widget.php:132
msgid "Display All Authors (including those who have not written any posts)"
msgstr ""

#: src/core/Authors_Widget.php:29
msgid "Display authors list."
msgstr ""

#: src/core/Plugin.php:1493 src/modules/author-boxes/author-boxes.php:803
msgid "Display Name"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:158
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:343
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:497
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:561
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:902
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1109
msgid "div"
msgstr ""

#: src/modules/divi-integration/divi-integration.php:52
msgid "Divi Integration"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1582
msgid "documentation page"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:194
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:950
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1187
msgid "Dotted"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:197
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:953
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1191
msgid "Double"
msgstr ""

#: src/core/Plugin.php:1444
msgid "Edit"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:328
#: src/modules/author-custom-fields/author-custom-fields.php:146
#, php-format
msgid "Edit %1$s"
msgstr ""

#: src/core/Plugin.php:555
#, fuzzy
#| msgid "Edit author"
msgid "Edit Author"
msgstr "Редагувати гостьового автора"

#: src/core/Classes/Author_Editor.php:157
#: src/core/Classes/Author_Editor.php:205
#, fuzzy
#| msgid "Edit Profile"
msgid "Edit Author Profile"
msgstr "Редагувати профіль"

#: src/modules/editflow-integration/editflow-integration.php:51
msgid "Edit Flow Integration"
msgstr ""

#: src/core/Plugin.php:505
msgid "Edit My Author Profile"
msgstr ""

#: src/core/Classes/Author_Editor.php:143
#: src/core/Classes/Author_Editor.php:220
msgid "Edit User"
msgstr ""

#: src/modules/elementor-integration/elementor-integration.php:57
msgid "Elementor Integration"
msgstr ""

#: src/core/Classes/Author_Editor.php:48 src/core/Classes/Author_Editor.php:360
#: src/modules/author-boxes/author-boxes.php:1354
#: src/modules/author-custom-fields/author-custom-fields.php:771
#: src/modules/author-boxes/classes/AuthorBoxesAjax.php:308
msgid "Email"
msgstr ""

#: src/core/CustomFieldsModel.php:44
msgid "Email address"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:721
msgid "Enable author pages:"
msgstr ""

#: src/core/Authors_Widget.php:136
msgid "Enable author's search box"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:577
msgid "Enable legacy \"Layouts\" feature:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:550
msgid "Enable PublishPress Authors for these post types:"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:240
msgid "Enabled features"
msgstr ""

#: src/core/Classes/Utils.php:1066
msgid "Enhance the power of PublishPress Authors with the Pro version:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1285
msgid "Enter class name without dot(.)"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3238
msgid "Error. Author terms could not be reseted."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:831
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1303
msgid "Export"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1531
msgid "Export Editor Settings"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:610
msgid "Fallback user for Guest Authors:"
msgstr ""

#: src/core/Classes/Utils.php:1072
msgid "Fast, professional support"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:844
msgid "Featured image custom height:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:833
msgid "Featured image custom width:"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:231
msgid "Features"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:232
msgid "Feel free to select only the features you need."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:592
msgid "Field Icon"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:588
msgid "Field Icon + Prefix + Value + Suffix"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:590
msgid "Field Prefix"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:593
msgid "Field Prefix + Value + Suffix"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:304
msgid "Field Slug"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:338
msgid "Field Status"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:591
msgid "Field Suffix"
msgstr ""

#: src/core/Plugin.php:1496
msgid "Field title is required"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:316
msgid "Field Type"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:589
msgid "Field Value"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:341
#: src/modules/author-custom-fields/author-custom-fields.php:159
#, php-format
msgid "Filter %2$s list"
msgstr ""

#: src/core/Authors_Widget.php:189 src/core/Classes/Author_Editor.php:350
#: src/modules/author-custom-fields/author-custom-fields.php:755
msgid "First Name"
msgstr "Ім’я"

#: src/modules/multiple-authors/multiple-authors.php:702
msgid "Font Awesome icons:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1352
#, php-format
msgid ""
"For authors_index layout, you can group user by profile field by using %1s ."
msgstr ""

#: src/core/Classes/Author_Editor.php:309
#: src/modules/multiple-authors/multiple-authors.php:2472
msgid "General"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1600
msgid "Generate Template"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:839
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1340
msgid "Generate Theme Template"
msgstr ""

#: src/modules/generatepress-integration/generatepress-integration.php:52
msgid "Generatepress Integration"
msgstr ""

#: src/modules/genesis-integration/genesis-integration.php:52
msgid "Genesis Integration"
msgstr ""

#: src/core/Classes/Author_Editor.php:371
msgid "Gravatar"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1600
msgid "Grid"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:754
msgid "Grid layout column:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:198
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:954
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1192
msgid "Groove"
msgstr ""

#: src/core/Classes/Author_Editor.php:99
msgid "Guest Author"
msgstr "Гостьовий автор"

#: src/core/Authors_Widget.php:179
msgid "Guest Authors"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1628
#: src/modules/multiple-authors/multiple-authors.php:1660
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:152
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:337
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:491
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:563
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:896
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1103
msgid "H1"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1629
#: src/modules/multiple-authors/multiple-authors.php:1661
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:153
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:338
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:492
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:564
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:897
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1104
msgid "H2"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1630
#: src/modules/multiple-authors/multiple-authors.php:1662
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:154
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:339
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:493
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:565
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:898
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1105
msgid "H3"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1631
#: src/modules/multiple-authors/multiple-authors.php:1663
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:155
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:340
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:494
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:566
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:899
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1106
msgid "H4"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1632
#: src/modules/multiple-authors/multiple-authors.php:1664
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:156
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:341
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:495
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:567
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:900
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1107
msgid "H5"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1633
#: src/modules/multiple-authors/multiple-authors.php:1665
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:157
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:342
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:496
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:568
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:901
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1108
msgid "H6"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1571
msgid "How to generate and use a theme template file"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:148
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:333
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:487
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:552
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:892
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1099
msgid "HTML Tag"
msgstr ""

#. Author URI of the plugin
msgid "https://publishpress.com"
msgstr ""

#. URI of the plugin
msgid "https://wordpress.org/plugins/publishpress-authors/"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1093
msgid "Icon Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:987
msgid "ID"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1573
msgid ""
"If enabled, PublishPress Authors will replace the default WordPress author "
"pages."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1549
msgid ""
"If the Author is mapped to a WordPress user, this will display the authors' "
"\"Display name\" and their \"Username\". The default is to show only the "
"\"Display name\". Showing the \"Username\" is useful if you have several "
"authors with similar names."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1184
#, php-format
msgid ""
"If you are having problems showing PublishPress Authors on author profile "
"pages, you can use this shortcode below. The argument %s forces the plugin "
"to retrieve the Author from the profile page and not any other posts on the "
"same screen."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1342
#, php-format
msgid ""
"If you are using the authors_recent layout, you can define the number of "
"columns by using %1s ."
msgstr ""

#: src/core/Plugin.php:698
#, php-format
msgid "If you like %s please leave us a %s rating. Thank you!"
msgstr ""

#: src/core/Classes/Utils.php:1087
msgid "If you need help or have a new feature request, let us know."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2138
msgid ""
"If you only have Guest Authors selected for a post, this user may be used as "
"a fallback. WordPress sometimes requires a WordPress user to be assigned to "
"each post. This user will not be visible on the front of your site."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:835
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1322
msgid "Import"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1562
msgid "Import Data"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1551
msgid "Import Editor Settings"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:30
msgid "Inline"
msgstr ""

#: src/modules/default-layouts/default-layouts.php:173
msgid "Inline (Legacy)"
msgstr ""

#: src/modules/default-layouts/default-layouts.php:174
msgid "Inline with avatar (Legacy)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:31
msgid "Inline with Avatars"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:200
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:956
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1194
msgid "Inset"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:287
msgid "Install this plugin to showcase content by your Authors."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1561
msgid "Invalid data"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3063
msgid "Invalid nonce"
msgstr ""

#: src/core/Plugin.php:1494
msgid "is required"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3886
msgid "It looks like you have Co-Authors Plus installed."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:110
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:295
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:437
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:731
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:853
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1055
msgid "Italic"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:137
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:322
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:464
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:762
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:880
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1082
msgid "Justify"
msgstr ""

#: src/core/Authors_Widget.php:190 src/core/Classes/Author_Editor.php:355
#: src/modules/author-custom-fields/author-custom-fields.php:763
msgid "Last Name"
msgstr "Прізвище"

#: src/core/Authors_Widget.php:131 src/core/Widget.php:123
msgid "Layout"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:742
msgid "Layout Slug"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1283
msgid "Layout Wrapper Class Name"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:661
msgid "Layout:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:134
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:319
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:461
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:759
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:877
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1079
msgid "Left"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:974
msgid "Limit"
msgstr ""

#: src/core/Authors_Widget.php:137
msgid "Limits per page"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:65
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:250
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:392
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:678
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:808
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1010
msgid "Line Height (px)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:123
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:308
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:450
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:746
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:866
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1068
msgid "Line Through"
msgstr ""

#: src/core/CustomFieldsModel.php:43
msgid "Link"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:560
msgid "link"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1599
msgid "List"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:97
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:282
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:424
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:716
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:840
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1042
msgid "Lowercase"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2476
msgid "Maintenance"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3249
msgid "Maintenance completed successfully."
msgstr ""

#: src/core/Classes/Author_Editor.php:338
msgid "Mapped User"
msgstr ""

#: src/core/Classes/Author_Editor.php:646
msgid "Mapped User (optional)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1134
msgid "Margin Bottom"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1140
msgid "Margin Left"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1146
msgid "Margin Right"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1128
msgid "Margin Top"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:811
msgid "Meta"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:475
msgid "Meta Background Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:469
msgid "Meta Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:481
msgid "Meta Link Hover Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:545
msgid "Meta Row"
msgstr ""

#: src/modules/byline-migration/byline-migration.php:72
msgid "Migrate Byline Data"
msgstr ""

#: src/modules/bylines-migration/bylines-migration.php:75
msgid "Migrate Bylines Data"
msgstr ""

#: src/core/Plugin.php:1566
msgid "Mine"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:985
msgid "Modified date"
msgstr ""

#: src/core/CustomFieldsModel.php:41
msgid "Multiline text"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:73
msgid "Multiple Authors"
msgstr ""

#: src/core/Authors_Widget.php:187 src/core/Classes/Author_Editor.php:47
#, fuzzy
#| msgid "Last Name"
msgid "Name"
msgstr "Прізвище"

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:546
msgid "Name Row"
msgstr ""

#: src/core/Classes/Utils.php:1082
msgid "Need PublishPress Authors Support?"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:329
#: src/modules/author-custom-fields/author-custom-fields.php:147
#, php-format
msgid "New %1$s"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:326
#: src/modules/author-custom-fields/author-custom-fields.php:144
#, php-format
msgid "New %1s"
msgstr ""

#: src/core/Plugin.php:558 src/core/Plugin.php:559
#, fuzzy
#| msgid "New author"
msgid "New Author"
msgstr "Новий гостьовий автор"

#: src/templates/parts/author-pages-list.php:156
#: src/templates/parts/author-pages-grid.php:163
msgid "Next"
msgstr ""

#: src/core/CustomFieldsModel.php:97
msgid "No"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:333
#: src/modules/author-custom-fields/author-custom-fields.php:151
#, php-format
msgid "No %2$s found"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:334
#: src/modules/author-custom-fields/author-custom-fields.php:152
#, php-format
msgid "No %2$s found in Trash"
msgstr ""

#: src/core/Classes/Post_Editor.php:160
msgid "No author term"
msgstr ""

#: src/core/Plugin.php:569
#, fuzzy
#| msgid "No guest authors found in Trash"
msgid "No authors found."
msgstr "В кошику не знайдено гостьових авторів"

#: src/core/Classes/Author_Editor.php:785
#, fuzzy
#| msgid "No guest authors found"
msgid "No authors were updated"
msgstr "Не знайдено гостьових авторів"

#: src/core/Plugin.php:882
msgid "No co-author exists for that term"
msgstr ""

#: src/core/Classes/Authors_Iterator.php:55
msgid ""
"No post ID provided for Authors_Iterator constructor. Are you not in a loop "
"or is $post not set?"
msgstr ""

#: src/core/WP_Cli.php:56
#, fuzzy
#| msgid "No guest authors found"
msgid "No posts without author terms were found"
msgstr "Не знайдено гостьових авторів"

#: src/modules/author-boxes/author-boxes.php:1387
msgid "No Recent Posts by this Author"
msgstr ""

#: src/core/Authors_Widget.php:375
msgid "No recent posts from this author"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:193
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:949
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1186
msgid "None"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:76
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:99
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:109
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:124
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:261
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:284
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:294
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:309
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:403
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:426
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:436
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:451
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:693
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:718
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:730
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:747
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:819
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:842
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:852
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:867
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1021
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1044
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1054
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1069
msgid "Normal"
msgstr ""

#: src/core/Classes/Installer.php:187
#, php-format
msgid "Now inspecting or updating %d total authors"
msgstr ""

#: src/core/Classes/Installer.php:294
#, php-format
msgid "Now inspecting or updating %d total posts"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:988
msgid "Number of comments"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:111
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:296
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:438
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:732
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:854
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1056
msgid "Oblique"
msgstr ""

#: src/core/CustomFieldsModel.php:79
#: src/modules/author-custom-fields/author-custom-fields.php:675
msgid "Optional"
msgstr ""

#: src/core/Authors_Widget.php:139
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:994
msgid "Order"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:980
msgid "Order By"
msgstr ""

#: src/core/Authors_Widget.php:140
msgid "Order by"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:201
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:957
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1195
msgid "Outset"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:122
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:307
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:449
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:745
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:865
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1067
msgid "Overline"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:160
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:345
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:499
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:562
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:904
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1111
msgid "p"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1158
msgid "Padding Bottom"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1164
msgid "Padding Left"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1170
msgid "Padding Right"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1152
msgid "Padding Top"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:148
msgid "Pages"
msgstr ""

#: src/core/Plugin.php:553
#, fuzzy
#| msgid "Parent author"
msgid "Parent Author"
msgstr "Гостьовий автор"

#: src/core/Plugin.php:554
#, fuzzy
#| msgid "Parent author:"
msgid "Parent Author:"
msgstr "Гостьовий автор"

#: src/modules/author-boxes/author-boxes.php:1552
msgid "Paste the editor data from the \"Export\" tab on another site."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2295
msgid ""
"Please be careful clicking these buttons. Before clicking, we recommend "
"taking a site backup in case anything goes wrong."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3887
msgid "Please click here and read this guide!"
msgstr ""

#: src/core/Plugin.php:1495
msgid "Please complete the following required fields to save your changes:"
msgstr ""

#: src/core/Classes/Legacy/LegacyPlugin.php:272
msgid "Please correct your form errors below and try again."
msgstr ""

#: src/core/Plugin.php:1484
msgid "Please, wait..."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1123
msgid "Plural"
msgstr ""

#: src/core/Widget.php:122
msgid "Plural Title"
msgstr ""

#: src/modules/polylang-integration/polylang-integration.php:55
#: src/modules/polylang-integration/polylang-integration.php:56
#: src/modules/polylang-integration/polylang-integration.php:57
msgid "Polylang Integration"
msgstr ""

#: src/core/Plugin.php:551
msgid "Popular Authors"
msgstr ""

#: src/core/Widget.php:42
#, fuzzy
#| msgid "Guest Author"
msgid "Post Author"
msgstr "Гостьовий автор"

#: src/core/Authors_Widget.php:188
msgid "Post Counts"
msgstr ""

#: src/core/Classes/Legacy/LegacyPlugin.php:281
#, fuzzy
#| msgid "Guest author does not exist"
msgid "Post does not exist"
msgstr "Гостьовий автор не існує"

#: src/templates/parts/author-pages-list.php:162
#: src/templates/parts/author-pages-grid.php:169
msgid "Post not found for the author"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:558
msgid "Post types to display on the author's profile page:"
msgstr ""

#: src/core/Plugin.php:760 src/core/Classes/Author_Editor.php:57
#: src/modules/multiple-authors/multiple-authors.php:147
msgid "Posts"
msgstr ""

#. Posts by a given author.
#: src/functions/template-tags.php:1154
#, php-format
msgid "Posts by %1$s"
msgstr ""

#: src/functions/template-tags.php:769
#, php-format
msgid "Posts by %s"
msgstr ""

#: src/templates/parts/author-pages-list.php:155
#: src/templates/parts/author-pages-grid.php:162
msgid "Prev"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1161
msgid "Preview as:"
msgstr ""

#: src/templates/parts/author-pages-list.php:116
msgid "Published date"
msgstr ""

#: src/core/Plugin.php:762 src/core/Classes/Author_Editor.php:59
#, php-format
msgid "Published posts of the following post types: %s"
msgstr ""

#. Author of the plugin
msgid "PublishPress"
msgstr ""

#. Name of the plugin
#: src/core/Plugin.php:695 src/modules/settings/settings.php:61
msgid "PublishPress Authors"
msgstr ""

#. Description of the plugin
#: src/modules/multiple-authors/multiple-authors.php:74
#: src/modules/multiple-authors/multiple-authors.php:78
msgid ""
"PublishPress Authors allows you to add multiple authors and guest authors to "
"WordPress posts"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:3959
msgid ""
"PublishPress Authors needs a database update for Permissions integration."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:804
msgid "PublishPress Authors Pro is required to add a new Custom Field."
msgstr ""

#: src/modules/settings/settings.php:101
msgid "PublishPress Authors Settings"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:289
msgid ""
"PublishPress Blocks has over 20 extra Gutenberg blocks including accordions, "
"galleries, tables, and more."
msgstr ""

#: src/modules/modules-settings/modules-settings.php:286
msgid ""
"PublishPress Blocks is a free plugin with full support for PublishPress "
"Authors."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:989
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1190
msgid "Random"
msgstr ""

#: src/modules/rank-math-seo-integration/rank-math-seo-integration.php:53
msgid "Rank Math Seo Integration"
msgstr ""

#: src/core/Classes/Utils.php:905
msgid "Read more."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:945
msgid "Recent Post Title Border Bottom Style"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:968
msgid "Recent Post Title Border Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:962
msgid "Recent Post Title Border Width"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:939
msgid "Recent Post Title Color"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:283
msgid "Recommendations for you"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:339
#: src/modules/author-custom-fields/author-custom-fields.php:157
#, php-format
msgid "Remove %1$s Image"
msgstr ""

#: src/core/Classes/Utils.php:1071
msgid "Remove PublishPress ads and branding"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:621
msgid "Remove single author map restriction:"
msgstr ""

#: src/core/Classes/Author_Editor.php:462
#: src/core/Classes/Author_Editor.php:511
msgid "Remove this image"
msgstr ""

#: src/core/Classes/Admin_Ajax.php:263 src/core/Classes/Admin_Ajax.php:329
msgid "Request status."
msgstr ""

#: src/core/Classes/Utils.php:1089
msgid "Request Support"
msgstr ""

#: src/core/CustomFieldsModel.php:80
#: src/modules/author-custom-fields/author-custom-fields.php:677
msgid "Required"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:347
#: src/modules/author-custom-fields/author-custom-fields.php:637
msgid "Requirement"
msgstr ""

#: src/modules/rest-api/rest-api.php:58
msgid "Rest API"
msgstr ""

#: src/modules/rest-api/rest-api.php:59 src/modules/rest-api/rest-api.php:60
msgid "Rest API support"
msgstr ""

#: src/modules/reviews/reviews.php:74
msgid "Reviews"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:199
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:955
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1193
msgid "Ridge"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:136
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:321
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:463
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:761
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:879
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1081
msgid "Right"
msgstr ""

#: src/core/Authors_Widget.php:388
msgid "Search"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:332
#: src/modules/author-custom-fields/author-custom-fields.php:150
#, php-format
msgid "Search %2$s"
msgstr ""

#: src/core/Plugin.php:550
#, fuzzy
#| msgid "Search authors"
msgid "Search Authors"
msgstr "Шукати гостьових авторів"

#: src/core/Authors_Widget.php:385
msgid "Search Box"
msgstr ""

#: src/core/Plugin.php:1453 src/core/Classes/Post_Editor.php:295
msgid "Search for an author"
msgstr ""

#: src/core/Classes/Post_Editor.php:350
msgid "Search for an user"
msgstr ""

#: src/core/Classes/Admin_Ajax.php:270 src/core/Classes/Admin_Ajax.php:336
#: src/modules/author-boxes/classes/AuthorBoxesAjax.php:40
#: src/modules/author-boxes/classes/AuthorBoxesAjax.php:103
msgid "Security error. Kindly reload this page and try again"
msgstr ""

#: src/core/Classes/Author_Editor.php:525
msgid "Select a user"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1168
msgid "Select Authors"
msgstr ""

#: src/core/Classes/Author_Editor.php:458
#: src/core/Classes/Author_Editor.php:507
msgid "Select image"
msgstr ""

#: src/core/Plugin.php:560
msgid "Separate authors with commas"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:338
#: src/modules/author-custom-fields/author-custom-fields.php:156
#, php-format
msgid "Set %1$s Image"
msgstr ""

#: src/modules/settings/settings.php:102
#: src/modules/multiple-authors/multiple-authors.php:373
msgid "Settings"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1561
#: src/modules/author-boxes/author-boxes.php:1566
msgid "Settings Imported Successfully!"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1251
msgid "Shadow Blur"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1233
msgid "Shadow Color"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1239
msgid "Shadow Horizontal Offset"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1257
msgid "Shadow spread"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1245
msgid "Shadow Vertical Offset"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:171
#: src/modules/author-boxes/author-boxes.php:758
msgid "Shortcode"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:913
msgid "Shortcode documentation."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2475
msgid "Shortcodes"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:368
msgid "Show \"View all posts\" link"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:531
#, php-format
msgid "Show %1s"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:539
#, php-format
msgid "Show %1s after"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:765
msgid "Show author bio:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:784
msgid "Show author page title:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:866
msgid "Show authors:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:177
msgid "Show Avatar"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:645
msgid "Show below the content:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:789
msgid "Show Biographical Info"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:899
msgid "Show category:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:888
msgid "Show comment counts:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:238
msgid "Show Display Name"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:374
msgid "Show Email field"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:686
#, fuzzy
#| msgid "Show all"
msgid "Show email link:"
msgstr "Показати все"

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:933
msgid "Show Even if No Recent Post"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:855
msgid "Show excerpt:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:822
msgid "Show featured image:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:362
msgid "Show Meta Information"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:877
msgid "Show post date:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:921
msgid "Show read more link:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:921
msgid "Show Recent Posts"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:927
msgid "Show Recent Title"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:694
msgid "Show site link:"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:910
msgid "Show tags:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:34
msgid "Show Title"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:588
msgid "Show username in the search field:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:380
msgid "Show Website field"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:284
msgid "Showcase your Authors with PublishPress Blocks"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesDefault.php:32
msgid "Simple List"
msgstr ""

#: src/modules/default-layouts/default-layouts.php:175
msgid "Simple list (Legacy)"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1116
msgid "Single"
msgstr ""

#: src/core/Widget.php:121
msgid "Single Title"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:321
#, php-format
msgctxt "singular author box post type name"
msgid "%1$s"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:139
#, php-format
msgctxt "singular custom field post type name"
msgid "%1$s"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:59
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:244
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:386
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:670
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:802
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1004
msgid "Size"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:638
msgid "Slug"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:325
msgid "Social Profile?"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:196
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:952
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1189
msgid "Solid"
msgstr ""

#: src/core/Plugin.php:1485
msgid "Sorry, the request returned an error."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:159
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:344
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:498
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:559
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:903
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1110
msgid "span"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:636
msgid "Status"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:104
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:289
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:431
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:723
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:847
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1049
msgid "Style"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2252
msgid "Synchronize Author slugs to User logins"
msgstr ""

#: src/core/Plugin.php:540
#, fuzzy
#| msgid "Authors"
msgctxt "taxonomy general name"
msgid "Authors"
msgstr "Автори"

#: src/core/Plugin.php:545
#, fuzzy
#| msgid "Authors"
msgctxt "taxonomy singular name"
msgid "Author"
msgstr "Автори"

#: src/modules/author-boxes/author-boxes.php:1614
msgid "Template generated successfuly!"
msgstr ""

#: src/core/CustomFieldsModel.php:40
msgid "Text"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1215
#, php-format
msgid ""
"The authors data shortcode accepts field parameter such as: %1s %2s %3s %4s "
"%5s %6s %7s. You can see full details and parameters %8s in this guide %9s."
msgstr ""

#: src/modules/seoframework-integration/seoframework-integration.php:58
msgid "The SEO Framework Integration"
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:307
msgid ""
"The slug allows only lowercase letters, numbers and underscore. It is used "
"as an attribute when referencing the author field."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:537
msgid "Theme"
msgstr ""

#: src/modules/settings/settings.php:201
msgid "There are no PublishPress modules registered"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1194
#, php-format
msgid ""
"There is one final option to mention. This is mostly useful if you're using "
"a theme or page builder to customize the Author profile pages you find at "
"URLs such as /author/username/. You can use the following shortcode on the "
"authors page to display the profile of the current author. You just need to "
"add the parameter %s."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:357
msgid ""
"This description appears under the fields and helps users understand their "
"choice."
msgstr ""

#: src/modules/author-custom-fields/author-custom-fields.php:329
msgid ""
"This feature will add the SameAs property to this link so that search "
"engines realize that the social profile is connected to this author."
msgstr ""

#: src/core/Plugin.php:514
msgid ""
"This forms part of the URL for the author’s profile page. If you choose a "
"Mapped User, this URL is taken from the user’s account and can not be "
"changed."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:574
msgid ""
"This is useful when linking to an email, URL, or phone number. For example, "
"'mailto:', 'https://' or 'tel:' can be added as the prefix."
msgstr ""

#: src/core/Plugin.php:509
msgid ""
"This name is used in several default displays and some search engine "
"integrations."
msgstr ""

#: src/core/Classes/Post_Editor.php:342
msgid ""
"This option is showing because you do not have a WordPress user selected as "
"an author. For some tasks, it can be helpful to have a user selected here. "
"This user will not be visible on the front of your site."
msgstr ""

#: publishpress-authors.php:63
msgid "This plugin can be deleted."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2102
msgid "This setting may be disabled for users who can not edit others posts."
msgstr ""

#: src/core/Classes/Author_Editor.php:827 src/core/Classes/Admin_Ajax.php:286
msgid "This user is already mapped to another author."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1896
msgid ""
"This will allows you map a WordPress user to more than one author. Don't use "
"this feature unless requested to do so by the PublishPress team. This plugin "
"works better when authors and users are paired one-to-one."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1848
msgid "This will display author page title."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1772
msgid "This will display the author bio."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1088
msgid "This will display the authors box at the end of the content."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2156
msgid "This will display the authors email in the author box."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2176
msgid "This will display the authors site in the author box."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1944
msgid "This will display the authors."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2016
msgid "This will display the categories."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1992
msgid "This will display the comment count."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1872
msgid "This will display the excerpt."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1824
msgid "This will display the featured image."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1968
msgid "This will display the published date."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2064
msgid "This will display the read more link."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2040
msgid "This will display the tags."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1920
msgid "This will enable legacy layout options."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2196
msgid "This will load Font Awesome icons for use in Author Boxes."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:2214
msgid "This will remove the Authors Box in \"Quick Edit\"."
msgstr ""

#: src/core/Authors_Widget.php:130
#: src/modules/author-boxes/author-boxes.php:795
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:986
msgid "Title"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:669
msgid "Title for the author box:"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:47
msgid "Title Text (Plural)"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:41
msgid "Title Text (Single)"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1386
#, php-format
msgid "To display a search box for authors, use %1s ."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1375
#, php-format
msgid "To further customize the order of results, use %1s or %2s ."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1362
#, php-format
msgid ""
"To order the results based on post count, use %1s . To order the results by "
"name, use %2s . Alternatively, you can order by profile fields like %3s, %4s "
"etc"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:91
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:276
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:418
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:708
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:834
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1036
msgid "Transform"
msgstr ""

#: src/modules/ultimatemember-integration/ultimatemember-integration.php:53
msgid "Ultimate Member Integration"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:121
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:306
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:448
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:744
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:864
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1066
msgid "Underline"
msgstr ""

#: src/core/Plugin.php:557
#, fuzzy
#| msgid "Update author"
msgid "Update Author"
msgstr "Оновити гостьового автора"

#: src/modules/multiple-authors/multiple-authors.php:2245
msgid "Update author field on posts"
msgstr ""

#: src/core/Classes/Author_Editor.php:712
msgid "Update data from mapped user"
msgstr ""

#: src/core/Classes/Author_Editor.php:720
#, fuzzy
#| msgid "Update Guest Author"
msgid "Update post count"
msgstr "Оновити гостьового автора"

#: src/core/Classes/Author_Editor.php:788
#, fuzzy, php-format
#| msgid "Update Guest Author"
msgid "Updated %d authors"
msgstr "Оновити гостьового автора"

#: src/core/Classes/Utils.php:1075
#: src/modules/author-custom-fields/author-custom-fields.php:807
msgid "Upgrade to Pro"
msgstr ""

#: src/core/Classes/Utils.php:1061
msgid "Upgrade to PublishPress Authors Pro"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:96
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:281
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:423
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:715
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:839
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1041
msgid "Uppercase"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:340
#: src/modules/author-custom-fields/author-custom-fields.php:158
#, php-format
msgid "Use as %1$s Image"
msgstr ""

#: src/modules/modules-settings/modules-settings.php:288
msgid ""
"Use the Content Display block to show your posts in many beautiful layouts."
msgstr ""

#: src/core/Classes/Author_Editor.php:97
msgid "User"
msgstr ""

#: src/core/Authors_Widget.php:180
msgid "Users Authors"
msgstr ""

#: src/core/Plugin.php:1492
msgid "View"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:331
#: src/modules/author-custom-fields/author-custom-fields.php:149
#, php-format
msgid "View %1$s"
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1349
#: src/modules/author-boxes/author-boxes.php:1350
#: src/modules/default-layouts/default-layouts.php:140
#, fuzzy
#| msgid "View Posts"
msgid "View all posts"
msgstr "Переглянути мареріали"

#: src/core/Plugin.php:556
#, fuzzy
#| msgid "View author"
msgid "View Author"
msgstr "Переглянути гостьового автора"

#: src/core/Classes/Utils.php:1100
msgid "View Knowledge Base"
msgstr ""

#: src/core/Classes/Author_Editor.php:386
#: src/modules/author-boxes/author-boxes.php:1359
#: src/modules/author-custom-fields/author-custom-fields.php:779
#: src/modules/author-boxes/classes/AuthorBoxesAjax.php:315
msgid "Website"
msgstr "Сайт"

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:71
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:256
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:398
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:686
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:814
#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1016
msgid "Weight"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1142
msgid ""
"With this shortcode you can display the author box in any part of the "
"content. "
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1207
msgid ""
"With this shortcode you can display the author names or any profile field in "
"any part of the content."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1269
msgid ""
"With this shortcode, you can show all the authors together in a single "
"display."
msgstr ""

#: src/modules/wpengine-integration/wpengine-integration.php:51
msgid "WPEngine Integration"
msgstr ""

#: src/core/CustomFieldsModel.php:42
msgid "WYSIWYG Editor"
msgstr ""

#: src/core/CustomFieldsModel.php:98
msgid "Yes, this is a Social Profile"
msgstr ""

#: src/modules/yoast-seo-integration/yoast-seo-integration.php:52
msgid "Yoast SEO Integration"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1254
#, php-format
msgid ""
"You can also decide to return array lists of authors for custom use or "
"formatting by using %1s which will return all authors object data as array. "
"You can check full details and sample usage %2s in this guide %3s"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1166
#, php-format
msgid ""
"You can also decide whether or not to show the main title, using %1s or %2s ."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1396
#, php-format
msgid ""
"You can also show a dropdown menu that allows users to search on specific "
"author fields. You can add fields to the dropdown using %1s . This requires "
"the search box to be active."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1232
msgid ""
"You can also specify the separator to be used for mulitple authors data."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1277
#, php-format
msgid ""
"You can choose from the following default layouts: %1s %2s %3s %4s %5s. You "
"can see full details of each layout option %6s in this guide %7s. %8s %9s "
"This shortcode also provides two custom layouts: %10s %11s."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1150
#, php-format
msgid ""
"You can choose from the following layouts: %1s %2s %3s %4s %5s. You can see "
"full details of each layout option %6s in this guide %7s."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1307
#, php-format
msgid ""
"You can choose the number of authors per page using %1s . %2s Pagination "
"will be automatically added if required."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1297
#, php-format
msgid ""
"You can define the number of layout columns by using %1s to show authors in "
"2 column."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1406
#, php-format
msgid ""
"You can limit the author list to users with a published post within a "
"specific period using %1s . This accept english date value like 1 week ago, "
"1 month ago, 6 months ago, 1 year ago etc."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1318
#, php-format
msgid ""
"You can limit the result to only authors who are assigned to posts by using "
"%1s . %2s Alternatively, use %3s to show all authors, including those "
"without any posts."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1330
#, php-format
msgid ""
"You can limit the result to only guest authors by using %1s . %2s "
"Alternatively, %3s will show only authors with a WordPress account."
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1176
#: src/modules/multiple-authors/multiple-authors.php:1239
msgid ""
"You can load the authors for a specific post, even if you are not in that "
"post currently. For example, this shortcode will load the authors for the "
"post with the ID of 32"
msgstr ""

#: src/modules/multiple-authors/multiple-authors.php:1246
msgid ""
"You can retrieve a specific author data by providing the author term id. For "
"example, this shortcode will load the author with term id of 102"
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:630
#, php-format
msgid ""
"You can use icons from Dashicons and Font Awesome. %1s %2sClick here for "
"documentation%3s."
msgstr ""

#: src/modules/author-boxes/classes/AuthorBoxesEditorFields.php:1284
msgid "You can use multiple class names. Leave a space between each class."
msgstr ""

#: src/modules/author-boxes/author-boxes.php:1532
msgid ""
"You can use this data to export your author box design and import it to a "
"new site."
msgstr ""

#: src/core/Classes/Legacy/LegacyPlugin.php:277
msgid "You do not have necessary permissions to complete this action."
msgstr ""

#: src/core/Classes/Author_Editor.php:653
msgid ""
"You don’t have to choose a Mapped User. Leave this choice blank and you can "
"create a Guest Author with no WordPress account."
msgstr ""
