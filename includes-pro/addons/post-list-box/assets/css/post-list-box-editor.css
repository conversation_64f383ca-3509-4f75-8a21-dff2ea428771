.post-list-boxes-group-table-wrap {
    position: relative;
    border: 1px solid #c3c4c7;
    padding: 15px;
}

.post-list-boxes-group-table-wrap .table-title {
    position: absolute;
    top: -12px;
    left: 10px;
    background: white;
    padding: 0 5px;
    font-weight: bold;
}

.post-list-boxes-group-table-wrap table {
    width: 100%;
    border-collapse: collapse;
}

.post-list-boxes-group-table-wrap th,
.post-list-boxes-group-table-wrap td {
    padding: 8px;
}

.post-list-field-icons-container .post-list-field-icons-tab-content {
    padding-bottom: 50px;
}

.pps-field-icon-tabs {
    padding-left: 1px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
    margin-bottom: 10px;
}

.post-list-field-icons-tab-button {
    display: inline-block;
    border: 1px solid #ccc;
    position: relative;
    padding: 10px;
    background: #f1f1f1;
    margin: 0 0 0 -1px;
    flex: 1;
    text-align: center;
    white-space: normal;
    cursor: pointer;
}

.post-list-field-icons-tab-button.active {
    border-color: #ccc;
    background: #ccc;
}

.post-list-field-icons-container .tab-content.hidden {
    display: none;
}

.post-list-field-icons-container .icon-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(calc(1em* 7.5), 1fr));
    grid-auto-rows: calc(1em* 7.5);
    grid-gap: 1em 1em;
    grid-auto-flow: row dense;
    justify-items: center;
    margin-bottom: calc(1em* 3);
    margin-top: calc(1em* 1.25);
}

.post-list-field-icons-container .icon-item {
    cursor: pointer;
    margin: 10px;
    text-align: center;
    padding: 2px;
}

.post-list-field-icons-container .icon-item.active,
.post-list-field-icons-container .icon-item:hover {
    background-color: #f1f1f1;
    border: 1px solid #ccc;
}

.post-list-field-icons-container .icon-item i,
.post-list-field-icons-container .icon-item span.dashicons {
    font-size: 24px;
}

.post-list-field-icons-container .icon-item span {
    display: block;
    margin-top: 5px;
    margin: 0 auto;
}

.post-list-field-icons-container .icon-item span.icon-element i,
.post-list-field-icons-container .icon-item span.icon-element span.dashicons {
    font-size: calc(1em * 2);
    text-align: center;
    width: 1.25em;
}

.post-list-field-icons-container .icon-item span.icon-name {
    font-size: calc(1em * 0.75);
    margin-top: calc(1em* 1.5);
    width: 100%;
    min-height: calc(1em* 2.125);
    line-height: 1.25;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    color: rgb(97 109 138);
    -webkit-box-orient: vertical;
}

.pps-field-icon-search {
    width: 100%;
    padding: 10px !important;
    margin-bottom: 10px;
    box-sizing: border-box;
    border: 1px solid #ccc !important;
    font-size: 16px;
    line-height: unset !important;
    border-radius: unset !important;
    font-weight: normal !important;
}

.icon-sticky-header {
    display: block;
    margin-top: 10px;
}

.popup-modal-header span.dashicons {
    vertical-align: middle;
}

#poststuff #pps_post_list_box_editor_area .inside {
    background: #f5f6fa;
    padding: 0;
    margin-top: 0;
}

.pp-multiple-post-list-boxes-wrapper h1,
.pp-multiple-post-list-boxes-wrapper h2,
.pp-multiple-post-list-boxes-wrapper h3,
.pp-multiple-post-list-boxes-wrapper h4,
.pp-multiple-post-list-boxes-wrapper h5,
.pp-multiple-post-list-boxes-wrapper h6 {
    padding: initial !important;
}

.pps-post-list-boxes-editor-table input[type=text],
.pps-post-list-boxes-editor-table input[type=number],
.pps-post-list-boxes-editor-table select {
    width: 95%;
}

.pps-post-list-boxes-editor-table textarea {
    width: 100%;
    resize: none;
}

.pps-post-list-boxes-editor-table button.wp-color-result {
    min-height: 30px !important;
    margin: 0 6px 6px 0 !important;
    padding: 0 0 0 30px !important;
    font-size: 11px !important;
}

.pps-post-list-boxes-editor-table .field-description {
    color: gray;
}

.pps-post-list-boxes-editor-table .post-list-boxes-field-icon {
    display: grid;
    gap: 10px;
}

.pps-post-list-boxes-editor-table .post-list-boxes-field-icon .remove-icon-button .button-secondary {
    color: red;
    border-color: red;
}

.pps-post-list-boxes-editor-table .post-list-boxes-field-icon .selected-field-icon {
    margin: auto;
}

.post-type-pps_post_list_box #slugdiv.postbox,
.post-type-pps_post_list_box label[for="slugdiv-hide"] {
    display: none !important;
}

.publishpress-post-list-box-editor {
    display: flex;
    background: #fff;
}

.publishpress-post-list-box-editor .pps-post-list-box-editor-tabs {
    min-width: 250px;
    margin: 0;
    line-height: 1em;
    padding: 0 0 10px;
    position: relative;
    background-color: #fafafa;
    border-right: 1px solid #eee;
    box-sizing: border-box;
}

.pps-post-list-box-editor-fields {
    width: 100%;
    min-width: 320px;
    padding: 10px;
    margin-top: 0;
    padding-top: 0;
}

.pps-boxes-editor-tab-content.pps-profile_fields-tab.profile_header .input {
    padding-left: 0;
}

.pps-editor-profile-header-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #c3c4c7;
    box-shadow: 0 1px 1px rgb(0 0 0 / 4%);
    background: #fff;
    border-bottom: 1px solid #c3c4c7;
    padding: 0;
    cursor: pointer;
}

.pps-editor-profile-header-title.closed .toggle-indicator::before {
    content: "\f140";
}

.pps-editor-profile-header-title .title-toggle button {
    border: none !important;
    background: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.publishpress-post-list-box-editor .pps-post-list-box-editor-tabs ul {
    padding-top: 0;
    margin-top: 0;
}

.publishpress-post-list-box-editor .pps-post-list-box-editor-tabs ul li a {
    margin: 0;
    padding: 10px;
    display: block;
    box-shadow: none;
    text-decoration: none;
    line-height: 20px !important;
    border-bottom: 1px solid #eee;
}

.publishpress-post-list-box-editor .pps-post-list-box-editor-tabs ul li {
    margin: 0;
    padding: 0;
    display: block;
    position: relative;
}

.publishpress-post-list-box-editor .pps-post-list-box-editor-tabs ul li a.active {
    color: #555;
    position: relative;
    background-color: #eee;
}

.publishpress-post-list-box-editor .pps-post-list-box-editor-tabs ul li a span {
    margin-right: 0.618em;
}

.publishpress-post-list-box-editor .pps-post-list-box-editor-tabs ul li a span.dashicons {
    margin-left: 0;
    font-size: 15px;
    vertical-align: sub;
}

.publishpress-post-list-box-editor .preview-section {
    width: 100%;
}

#pps_post_list_box_shortcode textarea {
    resize: none;
    width: 100%;
}

.editor-preview-post {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
}

.editor-preview-post .pps_select2-container {
    max-width: 250px !important;
    min-width: 250px !important;
}

.editor-preview-post-users .pps_select2-container {
    width: max-content !important;
    min-width: 100px;
    max-width: 700px;
}

.editor-preview-post-users .pps_select2-container--default .pps_select2-selection--multiple {
    height: 0 !important;
}

.editor-preview-post-users .pps_select2-container--default .pps_select2-selection--multiple .pps_select2-selection__clear {
    display: none;
}

.pps-editor-field-reorder-btn {
    cursor: pointer;
    color: #655997;
    margin-bottom: 10px;
    text-align: right;
}

.pps-editor-field-reorder-btn .dashicons {
    font-size: large;
}

.pps-thickbox-modal-content .pps-editor-order-form p {
    text-align: left;
}

.pps-re-order-lists .field-sort-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #c3c4c7;
    box-shadow: 0 1px 1px rgb(0 0 0 / 4%);
    background: #fff;
    border-bottom: 1px solid #c3c4c7;
    padding: 0;
    cursor: pointer;
    margin-bottom: 9px;
    line-height: 1.3;
    vertical-align: middle;
    cursor: move;
}

.pps-re-order-lists .field-sort-item h2 {
    font-size: 14px;
    padding: 8px 12px;
    margin: 0;
    line-height: 1.4;
    font-weight: 400;
}

.pps-editor-order-form .submit-wrapper {
    display: flex;
    justify-content: space-between;
}

.pps-order-response-message {
    text-align: left;
    margin-top: 10px;
}

.pps-order-response-message .success {
    color: green;
}

.pps-order-response-message .error {
    color: red;
}

.pps-editor-order-form .spinner:not(.is-active) {
    display: none;
}

.pps-post-list-boxes-editor-table .pps-boxes-editor-tab-content.code_editor .code-editor-label {
    font-weight: 600;
}

.pps-post-list-boxes-editor-table .code-mirror-before {
    border: 1px solid #ddd;
    border-bottom: none;
    background-color: #f7f7f7;
    margin-top: 10px;
}

.pps-post-list-boxes-editor-table .code-mirror-before div {
    color: #8F8F8F;
    margin-left: 54.35px;
    border-left: 1px solid #ddd;
    padding: 3px 8px;
    background-color: #fff;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.pps-post-list-boxes-editor-table .code-mirror-after {
    border: 1px solid #ddd;
    border-top: none;
    background-color: #f7f7f7;
}

.pps-post-list-boxes-editor-table .code-mirror-after div {
    color: #8F8F8F;
    margin-left: 54.35px;
    border-left: 1px solid #ddd;
    padding: 3px 8px;
    background-color: #fff;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.pps-post-list-boxes-editor-table .CodeMirror {
    margin-top: 0px;
    border: 1px solid #ddd;
    border-bottom: none;
    border-top: none;
}

.pps-post-list-boxes-editor-table .CodeMirror pre {
    padding-left: 7px;
    line-height: 1.25;
}

@media only screen and (max-width: 1270px) {

    .pps-post-list-boxes-editor-table input[type=text],
    .pps-post-list-boxes-editor-table input[type=number],
    .pps-post-list-boxes-editor-table select {
        width: 95%;
    }

    .pps-post-list-box-editor-fields {
        min-width: unset;
    }
}

/* Preview Styles */
.pps-post-list-box-preview {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.pps-post-list-box {
    padding: 20px;
}

.pps-post-list-box-preview h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.pps-post-list-title {
    margin: 0 0 20px 0;
    padding: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    line-height: 1.3;
}

.pps-post-list-container {
    display: flex;
    flex-direction: column;
}

.pps-post-list-item {
    display: flex;
    gap: 15px;
    align-items: flex-start;
    background: #fff;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.pps-post-list-box.pps-layout-grid .pps-post-list-item {
    flex-direction: column;
    align-items: stretch;
}

.pps-post-thumbnail {
    flex-shrink: 0;
}

.pps-post-thumbnail img {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 4px;
    background: #ddd;
}

.pps-post-content {
    flex: 1;
    min-width: 0;
}

.pps-post-title {
    margin: 0 0 8px 0;
    padding: 0;
    font-size: 18px;
    font-weight: 600;
    color: inherit;
    line-height: 1.4;
}

.pps-post-title a {
    text-decoration: none;
    color: inherit;
    transition: color 0.3s ease;
}

.pps-post-title a:hover {
    color: #007cba;
}

.pps-post-meta {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
    font-size: 13px;
    color: #888;
    margin-top: auto;
}

.pps-post-author,
.pps-post-date {
    display: inline-flex;
    align-items: center;
}

.pps-post-excerpt {
    margin: 8px 0;
    color: #666;
    line-height: 1.5;
    font-size: 14px;
}

.pps-post-excerpt p {
    margin: 0;
}

/* Layout styles */
.pps-layout-grid .pps-post-list-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    
}

.pps-layout-list .pps-post-list-container {
    display: flex;
    flex-direction: column;
    
}

@media only screen and (max-width: 1100px) {
    .pps-post-list-box-editor-fields {
        min-width: unset;
    }

    .pps-post-list-boxes-editor-table input[type=text],
    .pps-post-list-boxes-editor-table input[type=number],
    .pps-post-list-boxes-editor-table select,
    .pps-post-list-boxes-editor-table textarea,
    .pps-boxes-editor-tab-content.pps-profile_fields-tab.profile_header .input {
        max-width: 95% !important;
        width: 95% !important;
    }

    .publishpress-post-list-box-editor {
        display: block;
    }
}

@media screen and (max-width: 768px) {
    .publishpress-post-list-box-editor {
        display: block;
    }
}

/* Preview Loading States */
.pps-post-list-box-preview.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px 0 0 -15px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.pps-post-list-box-preview {
    position: relative;
}

.pps-post-list-box-preview.loading .preview-content {
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

/* Current Post Highlighting for Preview */
.pps-post-list-item.current-post {
    position: relative;
}

.pps-post-list-item.current-post .pps-post-title a {
    color: inherit;
    font-weight: 700;
}

.pps-post-list-item.current-post .pps-post-meta {
    color: inherit;
    opacity: 0.8;
}

/* Section Accordion Styles */
.pps-section-header-cell {
    padding: 0 !important;
    border-bottom: none !important;
}

.pps-section-header-wrapper {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #c3c4c7;
    border-radius: 6px;
    margin: 15px 0 8px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.pps-section-header-wrapper::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #2271b1 0%, #135e96 100%);
    transition: all 0.3s ease;
}

.pps-section-header-wrapper:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    border-color: #8c8f94;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.pps-section-header-wrapper:hover::before {
    width: 6px;
    background: linear-gradient(180deg, #135e96 0%, #0a4b78 100%);
}

.pps-section-title {
    margin: 0;
    padding: 14px 18px;
    font-size: 14px;
    font-weight: 600;
    color: #1d2327;
    display: flex;
    align-items: center;
    gap: 10px;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
}

.pps-section-toggle {
    font-size: 18px;
    color: #2271b1;
    transition: all 0.3s ease;
    margin-left: auto;
}

.pps-section-header-wrapper.collapsed .pps-section-toggle {
    transform: rotate(-90deg);
    color: #8c8f94;
}

.pps-section-header-wrapper:hover .pps-section-toggle {
    color: #135e96;
    transform: scale(1.1);
}

.pps-section-header-wrapper.collapsed:hover .pps-section-toggle {
    transform: rotate(-90deg) scale(1.1);
}

/* Conditional Field Styles */
.pps-conditional-field {
    transition: all 0.3s ease;
    opacity: 1;
    transform: translateY(0);
}

.pps-conditional-field.pps-field-hidden {
    display: none !important;
}

/* Add subtle animation when fields become visible */
.pps-conditional-field:not(.pps-field-hidden) {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Improve form field styling within sections */
.pps-section-title input,
.pps-section-container input,
.pps-section-content input,
.pps-section-thumbnail input,
.pps-section-meta input,
.pps-section-item_styling input,
.pps-section-structure input,
.pps-section-ordering input,
.pps-section-highlighting input,
.pps-section-title select,
.pps-section-container select,
.pps-section-content select,
.pps-section-thumbnail select,
.pps-section-meta select,
.pps-section-item_styling select,
.pps-section-structure select,
.pps-section-ordering select,
.pps-section-highlighting select {
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.pps-section-title input:focus,
.pps-section-container input:focus,
.pps-section-content input:focus,
.pps-section-thumbnail input:focus,
.pps-section-meta input:focus,
.pps-section-item_styling input:focus,
.pps-section-structure input:focus,
.pps-section-ordering input:focus,
.pps-section-highlighting input:focus,
.pps-section-title select:focus,
.pps-section-container select:focus,
.pps-section-content select:focus,
.pps-section-thumbnail select:focus,
.pps-section-meta select:focus,
.pps-section-item_styling select:focus,
.pps-section-structure select:focus,
.pps-section-ordering select:focus,
.pps-section-highlighting select:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

/* Section Field Grouping */
.pps-section-title,
.pps-section-content,
.pps-section-container,
.pps-section-thumbnail,
.pps-section-meta,
.pps-section-item_styling,
.pps-section-structure,
.pps-section-ordering,
.pps-section-highlighting {
    background: #fafafa;
    border-left: 4px solid #e1e5e9;
    transition: all 0.3s ease;
    position: relative;
}

.pps-section-title:hover,
.pps-section-content:hover,
.pps-section-container:hover,
.pps-section-thumbnail:hover,
.pps-section-meta:hover,
.pps-section-item_styling:hover,
.pps-section-structure:hover,
.pps-section-ordering:hover,
.pps-section-highlighting:hover {
    background: #f0f6fc;
    border-left-color: #2271b1;
}

/* Improved field spacing within sections */
.pps-section-title td,
.pps-section-container td,
.pps-section-content td,
.pps-section-thumbnail td,
.pps-section-meta td,
.pps-section-item_styling td,
.pps-section-structure td,
.pps-section-ordering td,
.pps-section-highlighting td {
    padding: 12px 20px 12px 24px;
    border-bottom: 1px solid #f0f0f1;
}

/* Section field labels */
.pps-section-title th,
.pps-section-container th,
.pps-section-content th,
.pps-section-thumbnail th,
.pps-section-meta th,
.pps-section-item_styling th,
.pps-section-structure th,
.pps-section-ordering th,
.pps-section-highlighting th {
    padding: 12px 15px 12px 24px;
    background: #f6f7f7;
    border-bottom: 1px solid #f0f0f1;
    font-weight: 600;
    color: #1d2327;
}

/* Better visual hierarchy */
.pps-section-header + .pps-section-title,
.pps-section-header + .pps-section-container,
.pps-section-header + .pps-section-content,
.pps-section-header + .pps-section-thumbnail,
.pps-section-header + .pps-section-meta,
.pps-section-header + .pps-section-item_styling,
.pps-section-header + .pps-section-structure,
.pps-section-header + .pps-section-ordering,
.pps-section-header + .pps-section-highlighting {
    border-top: none;
}

/* Responsive improvements for sections */
@media only screen and (max-width: 768px) {
    .pps-section-title {
        font-size: 13px;
        padding: 10px 12px;
    }

    .pps-section-toggle {
        font-size: 14px;
    }

    .pps-section-title td,
    .pps-section-container td,
    .pps-section-content td,
    .pps-section-thumbnail td,
    .pps-section-meta td,
    .pps-section-item_styling td,
    .pps-section-structure td,
    .pps-section-ordering td,
    .pps-section-highlighting td {
        padding-left: 15px;
    }
}