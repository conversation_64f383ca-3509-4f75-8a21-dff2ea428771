jQuery(document).ready(function ($) {
    
    function ppsPostListBoxEditorInit() {
        if (typeof postListBoxEditor === 'undefined') {
            return;
        }

        // Tab switching functionality
        $('.pps-post-list-box-editor-tabs a').on('click', function (e) {
            e.preventDefault();

            var tab = $(this).data('tab');

            $('.pps-post-list-box-editor-tabs a').removeClass('active');
            $(this).addClass('active');

            $('.pps-boxes-editor-tab-content').hide();
            $('.pps-' + tab + '-tab').show();
        });

        // Color picker initialization
        $('.pps-editor-color-picker').wpColorPicker({
            change: function(event, ui) {
                // Handle color change
                debouncedUpdate();
            },
            clear: function() {
                // Handle clear button click
                updatePreview();
            }
        });

        // Code mirror initialization
        var cssEditorSettings = wp.codeEditor.defaultSettings ? _.clone(wp.codeEditor.defaultSettings) : {};
        cssEditorSettings.codemirror = _.extend(
            {},
            cssEditorSettings.codemirror,
            {
                indentUnit: 2,
                tabSize: 2,
                mode: 'css',
            }
        );

        var codeMirrorInstances = [];
        $('.pps-post-list-code-editor').each(function () {
            var editor = wp.codeEditor.initialize($(this), cssEditorSettings);
            if (editor && editor.codemirror) {
                codeMirrorInstances.push(editor.codemirror);
            }
        });

        // Preview functionality
        function updatePreview() {
            var postId = postListBoxEditor.post_id;
            
            // Collect all current settings from the entire post form for robustness
            // (some fields may live outside the local container depending on filters)
            var formData = $('#post').serialize();
            
            // Get the currently selected series ID from the preview selector
            var seriesId = $('#pps-preview-series-select').val() || 0;
            
            // Show loading state
            $('.pps-post-list-box-preview').addClass('loading');
            try { console.debug('[PPS] updatePreview triggered with series:', seriesId); } catch(e) {}
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'pps_update_post_list_box_preview',
                    post_id: postId,
                    settings: formData,
                    series_id: seriesId,
                    nonce: postListBoxEditor.nonce
                },
                success: function (response) {
                    if (response && response.success && response.data && response.data.preview) {
                        // Replace only the preview content, not the entire section
                        $('#pps-preview-content').html(response.data.preview);
                    }
                    $('.pps-post-list-box-preview').removeClass('loading');
                },
                error: function () {
                    $('.pps-post-list-box-preview').removeClass('loading');
                   $('#pps-preview-content').html('<div class="notice notice-error"><p>Failed to update preview. Please try again.</p></div>');
                }
            });
        }

        // Update preview when form fields change (with debounce)
        var previewTimeout;
        function debouncedUpdate() {
            clearTimeout(previewTimeout);
            previewTimeout = setTimeout(updatePreview, 300);
            try { console.debug('[PPS] debouncedUpdate scheduled'); } catch(e) {}
        }

        // Delegate to capture dynamically added inputs as well, but exclude the series selector
        $(document)
            .on('change input keyup click', '.pps-post-list-box-editor-fields input, .pps-post-list-box-editor-fields select, .pps-post-list-box-editor-fields textarea', debouncedUpdate)
            .on('change input keyup click', '.pps-post-list-boxes-editor-table input, .pps-post-list-boxes-editor-table select, .pps-post-list-boxes-editor-table textarea', debouncedUpdate)
            // Extra-safe: watch any input/select/textarea within the editor wrapper, but exclude series selector
            .on('change input keyup click', '.publishpress-post-list-box-editor :input:not(#pps-preview-series-select)', debouncedUpdate);


        // Bind CodeMirror change to update preview in realtime
        if (codeMirrorInstances.length) {
            codeMirrorInstances.forEach(function (cm) {
                cm.on('change', debouncedUpdate);
            });
        }

        // Form validation
        $('form#post').on('submit', function (e) {
            var title = $('#title').val();
            if (!title || title.trim() === '') {
                alert('Please enter a title for the Post List Box.');
                e.preventDefault();
                return false;
            }
        });

        // Export functionality
        $(document).on('click', '.pps-export-button', function (e) {
            e.preventDefault();
            
            var postId = postListBoxEditor.post_id;
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'pps_export_post_list_box',
                    post_id: postId,
                    nonce: postListBoxEditor.nonce
                },
                success: function (response) {
                    if (response.success) {
                        var data = response.data;
                        var filename = 'post-list-box-' + data.slug + '.json';
                        var blob = new Blob([JSON.stringify(data.settings, null, 2)], {type: 'application/json'});
                        var url = URL.createObjectURL(blob);
                        var a = document.createElement('a');
                        a.href = url;
                        a.download = filename;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                    }
                }
            });
        });

        // Import functionality
        $(document).on('click', '.pps-import-button', function (e) {
            e.preventDefault();
            
            var fileInput = $('#pps-import-file')[0];
            var file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a file to import.');
                return;
            }
            var reader = new FileReader();
            reader.onload = function (e) {
                try {
                    var data = JSON.parse(e.target.result);
                    
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'pps_import_post_list_box',
                            settings: data,
                            post_id: postListBoxEditor.post_id,
                            nonce: postListBoxEditor.nonce
                        },
                        success: function (response) {
                            if (response.success) {
                                alert('Settings imported successfully!');
                                location.reload();
                            } else {
                                alert('Error importing settings: ' + response.data);
                            }
                        }
                    });
                } catch (error) {
                    alert('Invalid JSON file format: ' + error.message);
                }
            };
            reader.onerror = function () {
                alert('Failed to read the file. Please try again.');
            };
            reader.readAsText(file);
        });

        // Reset to defaults
        $(document).on('click', '.pps-reset-button', function (e) {
            e.preventDefault();
            
            if (confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'pps_reset_post_list_box',
                        post_id: postListBoxEditor.post_id,
                        nonce: postListBoxEditor.nonce
                    },
                    success: function (response) {
                        if (response.success) {
                            alert('Settings reset to defaults!');
                            location.reload();
                        }
                    }
                });
            }
        });

        // Shortcode copying
        $(document).on('click', '.pps-copy-shortcode', function (e) {
            e.preventDefault();
            
            var shortcode = $(this).data('shortcode');
            navigator.clipboard.writeText(shortcode).then(function () {
                alert('Shortcode copied to clipboard!');
            });
        });
    }

    // Initialize the editor
    ppsPostListBoxEditorInit();
    // Trigger an initial preview so UI reflects defaults immediately
    jQuery(function(){
        if (typeof postListBoxEditor !== 'undefined') {
            // Slight delay to ensure DOM is ready
            setTimeout(function(){
                jQuery(document).trigger('post-list-box-editor-reinit');
                // Also invoke update directly if available
                try { 
                    // Fire a direct update to guarantee first render
                    jQuery(document).trigger('pps-post-list-box-update-preview');
                } catch(e) {}
            }, 50);
        }
    });

    // Re-initialize when needed (e.g., after AJAX calls)
    $(document).on('post-list-box-editor-reinit', ppsPostListBoxEditorInit);
    // Explicit event hook to trigger preview updates from anywhere if needed
    $(document).on('pps-post-list-box-update-preview', function(){
        try {
            if (typeof postListBoxEditor !== 'undefined') {
                // call updater directly for reliability
                if (typeof updatePreview === 'function') {
                    updatePreview();
                } else {
                    // fallback to debounced path
                    var evt = jQuery.Event('input');
                    jQuery('.publishpress-post-list-box-editor :input:first').trigger(evt);
                }
            }
        } catch (e) {}
    });

    // Handle responsive behavior
    function handleResponsiveLayout() {
        var windowWidth = $(window).width();
        
        if (windowWidth <= 768) {
            $('.publishpress-post-list-box-editor').addClass('mobile-view');
        } else {
            $('.publishpress-post-list-box-editor').removeClass('mobile-view');
        }
    }

    $(window).on('resize', handleResponsiveLayout);
    handleResponsiveLayout();

    // Handle form field dependencies
    function handleFieldDependencies() {
        // Check all conditional fields on page load
        checkAllConditionalFields();

        // Listen for changes on any input that might affect conditional fields
        $(document).on('change input', '.pps-post-list-box-editor-fields input, .pps-post-list-box-editor-fields select', function() {
            var fieldName = $(this).attr('name');
            if (fieldName) {
                // Extract the field key from the name attribute (remove prefix)
                var fieldKey = fieldName.replace(/^pps_post_list_box_/, '');
                checkConditionalFieldsForDependency(fieldKey);
            }
        });
    }

    function checkAllConditionalFields() {
        $('.pps-conditional-field').each(function() {
            var $field = $(this);
            var dependsOn = $field.data('depends-on');
            var dependsValue = $field.data('depends-value');

            if (dependsOn) {
                var $dependencyField = $('[name="pps_post_list_box_' + dependsOn + '"]');
                if ($dependencyField.length) {
                    var currentValue = getDependencyFieldValue($dependencyField);
                    toggleConditionalField($field, currentValue, dependsValue);
                }
            }
        });
    }

    function checkConditionalFieldsForDependency(changedFieldKey) {
        $('.pps-conditional-field[data-depends-on="' + changedFieldKey + '"]').each(function() {
            var $field = $(this);
            var dependsValue = $field.data('depends-value');
            var $dependencyField = $('[name="pps_post_list_box_' + changedFieldKey + '"]');

            if ($dependencyField.length) {
                var currentValue = getDependencyFieldValue($dependencyField);
                toggleConditionalField($field, currentValue, dependsValue);
            }
        });
    }

    function getDependencyFieldValue($field) {
        if ($field.is(':checkbox')) {
            return $field.is(':checked') ? '1' : '0';
        } else {
            return $field.val();
        }
    }

    function toggleConditionalField($field, currentValue, expectedValue) {
        if (currentValue === expectedValue) {
            $field.show().removeClass('pps-field-hidden');
        } else {
            $field.hide().addClass('pps-field-hidden');
        }
    }

    handleFieldDependencies();

    // Handle section accordion functionality
    function initSectionAccordions() {
        // Handle section toggle clicks
        $(document).on('click', '.pps-section-header-wrapper', function(e) {
            e.preventDefault();
            var $wrapper = $(this);
            var section = $wrapper.data('section');
            var $toggle = $wrapper.find('.pps-section-toggle');
            var $sectionFields = $('.pps-section-' + section).not('.pps-section-header');

            if ($wrapper.hasClass('collapsed')) {
                // Expand section
                $wrapper.removeClass('collapsed');
                $toggle.removeClass('dashicons-arrow-right').addClass('dashicons-arrow-down');
                $sectionFields.slideDown(200);
            } else {
                // Collapse section
                $wrapper.addClass('collapsed');
                $toggle.removeClass('dashicons-arrow-down').addClass('dashicons-arrow-right');
                $sectionFields.slideUp(200);
            }
        });

        // Initialize collapsed sections
        $('.pps-section-header-wrapper.collapsed').each(function() {
            var $wrapper = $(this);
            var section = $wrapper.data('section');
            var $toggle = $wrapper.find('.pps-section-toggle');
            var $sectionFields = $('.pps-section-' + section).not('.pps-section-header');

            $toggle.removeClass('dashicons-arrow-down').addClass('dashicons-arrow-right');
            $sectionFields.hide();
        });
    }

    initSectionAccordions();

    // Add loading states
    function showLoading($element) {
        $element.addClass('loading');
        $element.prop('disabled', true);
    }

    function hideLoading($element) {
        $element.removeClass('loading');
        $element.prop('disabled', false);
    }

    // Tooltips for help text
    $(document).on('mouseenter', '.pps-help-tooltip', function () {
        var tooltipText = $(this).data('tooltip');
        if (tooltipText) {
            var $tooltip = $('<div class="pps-tooltip">' + tooltipText + '</div>');
            $('body').append($tooltip);
            
            $(this).on('mousemove', function (e) {
                $tooltip.css({
                    top: e.pageY + 10,
                    left: e.pageX + 10
                });
            });
        }
    });

    $(document).on('mouseleave', '.pps-help-tooltip', function () {
        $('.pps-tooltip').remove();
    });

    // Quick save functionality
    $(document).on('click', '.pps-quick-save', function (e) {
        e.preventDefault();
        
        var $form = $('#post');
        var formData = $form.serialize();
        
        showLoading($(this));
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData + '&action=pps_quick_save_post_list_box&nonce=' + postListBoxEditor.nonce,
            success: function (response) {
                hideLoading($('.pps-quick-save'));
                
                if (response.success) {
                    $('.pps-save-message').text('Saved!').addClass('success').show().delay(2000).fadeOut();
                } else {
                    $('.pps-save-message').text('Error saving').addClass('error').show().delay(3000).fadeOut();
                }
            },
            error: function () {
                hideLoading($('.pps-quick-save'));
                $('.pps-save-message').text('Connection error').addClass('error').show().delay(3000).fadeOut();
            }
        });
    });
});