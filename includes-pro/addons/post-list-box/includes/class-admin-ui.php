<?php
/**
 * Admin UI for Post List Box
 */

class PPS_Post_List_Box_Admin_UI {

    const POST_TYPE_BOXES = 'pps_post_list_box';
    const META_PREFIX = 'pps_post_list_box_';

    /**
     * Initialize admin UI
     */
    public static function init() {
        add_action('admin_menu', [__CLASS__, 'admin_submenu'], 50);
        add_filter('post_updated_messages', [__CLASS__, 'set_post_update_messages']);
        add_filter('bulk_post_updated_messages', [__CLASS__, 'set_post_bulk_update_messages'], 10, 2);
        add_action('add_meta_boxes', [__CLASS__, 'add_preview_metabox']);
        add_action('add_meta_boxes', [__CLASS__, 'add_editor_metabox']);
        add_action('add_meta_boxes', [__CLASS__, 'add_shortcode_metabox']);
        add_filter('manage_edit-' . self::POST_TYPE_BOXES . '_columns', [__CLASS__, 'filter_post_list_box_columns']);
        add_action('manage_' . self::POST_TYPE_BOXES . '_posts_custom_column', [__CLASS__, 'manage_post_list_box_columns'], 10, 2);
        add_filter('parent_file', [__CLASS__, 'set_parent_file']);
    }

    /**
     * Set series menu as parent for post type so menu is shown
     * as active when on post type edit screen.
     *
     * @param string $parent_file
     *
     * @return string
     */
    public static function set_parent_file($parent_file)
    {
        global $submenu_file, $current_screen;

        // Check if the current screen is the Post List Box page
        if (!empty($current_screen->post_type) && $current_screen->post_type == self::POST_TYPE_BOXES) {
            $parent_file = 'orgseries_options_page';
            $submenu_file = 'edit.php?post_type=' . self::POST_TYPE_BOXES;
        }

        return $parent_file;
    }

    /**
     * @param $columns
     *
     * @return array
     */
    public static function filter_post_list_box_columns($columns)
    {
        $columns['shortcode'] = esc_html__('Shortcode', 'publishpress-series-pro');
        unset($columns['date']);

        return $columns;
    }

    /**
     * @param $column
     * @param $postId
     */
    public static function manage_post_list_box_columns($column, $postId)
    {
        if ($column === 'shortcode') {
            $layout_slug = self::POST_TYPE_BOXES . '_' . $postId;
        ?>
            <input readonly type="text" value='[pps_post_list_box layout="<?php echo esc_attr($layout_slug); ?>"]' />
        <?php
        }
    }

    /**
     * Add the admin submenu.
     */
    public static function admin_submenu()
    {
        // Add the submenu to the PublishPress Series menu.
        add_submenu_page(
            'orgseries_options_page',
            esc_html__('Post List Boxes', 'organize-series'),
            esc_html__('Post List Boxes', 'organize-series'),
            'manage_publishpress_series',
            'edit.php?post_type=' . self::POST_TYPE_BOXES
        );
    }

    /**
     * Add custom update messages to the post_updated_messages filter flow.
     *
     * @param array $messages Post updated messages.
     *
     * @return  array   $messages
     */
    public static function set_post_update_messages($messages)
    {
        $messages[self::POST_TYPE_BOXES] = [
            1 => __('Post List Box updated.', 'publishpress-series-pro'),
            4 => __('Post List Box updated.', 'publishpress-series-pro'),
            6 => __('Post List Box added.', 'publishpress-series-pro'),
            7 => __('Post List Box saved.', 'publishpress-series-pro'),
            8 => __('Post List Box submitted.', 'publishpress-series-pro'),
        ];

        return $messages;
    }

    /**
     * Add custom update messages to the bulk_post_updated_messages filter flow.
     *
     * @param array $messages Array of messages.
     * @param array $counts Array of item counts for each message.
     *
     * @return  array   $messages
     */
    public static function set_post_bulk_update_messages($messages, $counts)
    {
        $countsUpdated = (int)$counts['updated'];
        $countsLocked = (int)$counts['locked'];
        $countsDeleted = (int)$counts['deleted'];
        $countsTrashed = (int)$counts['trashed'];
        $countsUntrashed = (int)$counts['untrashed'];

        $postTypeNameSingular = __('Post List Box', 'publishpress-series-pro');
        $postTypeNamePlural = __('Post List Boxes', 'publishpress-series-pro');

        $messages[self::POST_TYPE_BOXES] = [
            'updated' => sprintf(
                _n('%1$s %2$s updated.', '%1$s %3$s updated.', $countsUpdated),
                $countsUpdated,
                $postTypeNameSingular,
                $postTypeNamePlural
            ),
            'locked' => sprintf(
                _n(
                    '%1$s %2$s not updated, somebody is editing it.',
                    '%1$s %3$s not updated, somebody is editing them.',
                    $countsLocked
                ),
                $countsLocked,
                $postTypeNameSingular,
                $postTypeNamePlural
            ),
            'deleted' => sprintf(
                _n('%1$s %2$s permanently deleted.', '%1$s %3$s permanently deleted.', $countsDeleted),
                $countsDeleted,
                $postTypeNameSingular,
                $postTypeNamePlural
            ),
            'trashed' => sprintf(
                _n('%1$s %2$s moved to the Trash.', '%1$s %3$s moved to the Trash.', $countsTrashed),
                $countsTrashed,
                $postTypeNameSingular,
                $postTypeNamePlural
            ),
            'untrashed' => sprintf(
                _n('%1$s %2$s restored from the Trash.', '%1$s %3$s restored from the Trash.', $countsUntrashed),
                $countsUntrashed,
                $postTypeNameSingular,
                $postTypeNamePlural
            ),
        ];

        return $messages;
    }

    /**
     * Add editor metabox
     *
     * @return void
     */
    public static function add_preview_metabox()
    {
        add_meta_box(
            self::META_PREFIX . 'preview_area',
            __('Post List Box Preview', 'publishpress-series-pro'),
            [__CLASS__, 'render_preview_metabox'],
            self::POST_TYPE_BOXES,
            'normal',
            'high'
        );
    }

    /**
     * Add editor metabox
     *
     * @return void
     */
    public static function add_editor_metabox()
    {
        add_meta_box(
            self::META_PREFIX . 'editor_area',
            __('Post List Box Editor', 'publishpress-series-pro'),
            [__CLASS__, 'render_editor_metabox'],
            self::POST_TYPE_BOXES,
            'normal',
            'high'
        );
    }

    /**
     * Add shortcode metabox
     *
     * @return void
     */
    public static function add_shortcode_metabox()
    {
        add_meta_box(
            self::META_PREFIX . 'shortcode',
            __('Shortcode', 'publishpress-series-pro'),
            [__CLASS__, 'render_shortcode_metabox'],
            self::POST_TYPE_BOXES,
            'side'
        );
    }

    /**
     * Render layout slug metaboxes
     *
     * @param \WP_Post $post
     * @return void
     */
    public static function render_layout_slug_metabox(\WP_Post $post)
    {
        $layout_slug = self::POST_TYPE_BOXES . '_' . $post->ID;
    ?>
        <input type="text" value="<?php echo esc_attr($layout_slug); ?>" readonly />
    <?php
    }

    /**
     * Render shortcode metaboxes
     *
     * @param \WP_Post $post
     * @return void
     */
    public static function render_shortcode_metabox(\WP_Post $post)
    {
        $layout_slug = self::POST_TYPE_BOXES . '_' . $post->ID;
    ?>
        <textarea readonly>[pps_post_list_box layout="<?php echo esc_attr($layout_slug); ?>"]</textarea>
        <p class="description"><a href="#"><?php esc_html_e('Shortcode documentation.', 'publishpress-series-pro'); ?></a></p>
    <?php
    }

    /**
     * Render preview metabox
     *
     * @param \WP_Post $post
     *
     * @return void
     */
    public static function render_preview_metabox(\WP_Post $post)
    {
        $fields = apply_filters('pps_post_list_box_editor_fields', PPS_Post_List_Box_Fields::get_fields($post), $post);

        if ($post->post_status === 'auto-draft') {
            $editor_data = PPS_Post_List_Box_Fields::get_post_list_box_layout_meta_values($post->ID, true);
        } else {
            $editor_data = PPS_Post_List_Box_Fields::get_post_list_box_layout_meta_values($post->ID);
        }

        // Get all available series for the dropdown
        $all_series = get_terms([
            'taxonomy' => 'series',
            'hide_empty' => false,
            'orderby' => 'name',
            'order' => 'ASC',
        ]);

        $preview_series_id = 0;
        if (!empty($all_series) && !is_wp_error($all_series)) {
            $preview_series_id = $all_series[0]->term_id;
        }
        
        ?>
        <div class="pressshack-admin-wrapper publishpress-post-list-box-editor">
            <div class="preview-section wrapper-column">
                <div class="pps-series-selector-container" style="margin-bottom: 20px;">
                    <?php if (!empty($all_series) && !is_wp_error($all_series)) : ?>
                        <div class="pps-series-selector">
                            <label for="pps-preview-series-select">
                                <?php esc_html_e('Select Series to Preview:', 'publishpress-series-pro'); ?>
                            </label>
                            <select id="pps-preview-series-select" style="margin-left: 10px; min-width: 200px;">
                                <?php foreach ($all_series as $series) : ?>
                                    <option value="<?php echo esc_attr($series->term_id); ?>">
                                        <?php echo esc_html($series->name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="pps-post-list-box-preview">
                    
                    <div id="pps-preview-content">
                        <?php 
                        if (empty($all_series) || is_wp_error($all_series)) {
                            echo '<p>' . esc_html__('No series found. Create a series to see the preview.', 'publishpress-series-pro') . '</p>';
                        } else {
                            // Initial preview with first series
                            $layout_slug = self::POST_TYPE_BOXES . '_' . $post->ID;
                            
                            // Load the renderer if not already loaded
                            if (!class_exists('PostListBoxRenderer')) {
                                require_once __DIR__ . '/classes/PostListBoxRenderer.php';
                            }
                            
                            // Create sample posts for preview
                            $sample_posts = PPS_Post_List_Box_Preview::get_sample_series_posts($preview_series_id);
                            if (!empty($sample_posts)) {
                                echo PPS_Post_List_Box_Preview::render_preview_content($editor_data, $sample_posts);
                            } else {
                                echo '<p>' . esc_html__('No posts found in selected series.', 'publishpress-series-pro') . '</p>';
                            }
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
        
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Store the current selected series to maintain state
            var currentSeriesId = $('#pps-preview-series-select').val();
            
            $('#pps-preview-series-select').on('change', function() {
                currentSeriesId = $(this).val();
                var postId = <?php echo esc_js($post->ID); ?>;
                
                $('#pps-preview-content').html('<p><?php esc_html_e('Loading preview...', 'publishpress-series-pro'); ?></p>');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'pps_update_post_list_box_preview',
                        post_id: postId,
                        series_id: currentSeriesId,
                        nonce: '<?php echo wp_create_nonce('post-list-box-nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#pps-preview-content').html(response.data.preview);
                        } else {
                            $('#pps-preview-content').html('<p><?php esc_html_e('Error loading preview.', 'publishpress-series-pro'); ?></p>');
                        }
                    },
                    error: function() {
                        $('#pps-preview-content').html('<p><?php esc_html_e('Error loading preview.', 'publishpress-series-pro'); ?></p>');
                    }
                });
            });
            
            // Prevent the series selector from triggering the global preview update
            $('#pps-preview-series-select').on('change input keyup click', function(e) {
                e.stopPropagation();
            });
        });
        
        </script>
        <?php
    }

    /**
     * Render editor metabox
     *
     * @param \WP_Post $post
     *
     * @return void
     */
    public static function render_editor_metabox(\WP_Post $post)
    {
        $fields_tabs  = apply_filters('pps_post_list_box_editor_fields_tabs', PPS_Post_List_Box_Fields::get_fields_tabs($post), $post);
        $fields = apply_filters('pps_post_list_box_editor_fields', PPS_Post_List_Box_Fields::get_fields($post), $post);
        ?>
        <div class="pressshack-admin-wrapper publishpress-post-list-box-editor">
            <div class="pps-post-list-box-editor-tabs">
                <ul>
                    <?php
                    foreach ($fields_tabs as $key => $args) {
                        $active_tab = ($key === PPS_Post_List_Box_Fields::default_tab()) ? ' active' : ''; ?>
                    <li>
                        <a data-tab="<?php esc_attr_e($key); ?>"
                            class="<?php esc_attr_e($active_tab); ?>"
                            href="#"
                            >
                            <span class="<?php esc_attr_e($args['icon']); ?>"></span>
                            <span class="item"><?php esc_html_e($args['label']); ?></span>
                        </a>
                    </li>
                    <?php
                    } ?>
                </ul>
            </div>

            <div class="pps-post-list-box-editor-fields wrapper-column">
                <table class="form-table pps-post-list-boxes-editor-table fixed" role="presentation">
                    <tbody>
                        <?php
                        if ($post->post_status === 'auto-draft') {
                            $editor_data = PPS_Post_List_Box_Fields::get_post_list_box_layout_meta_values($post->ID, true);
                        } else {
                            $editor_data = PPS_Post_List_Box_Fields::get_post_list_box_layout_meta_values($post->ID);
                        }

                        foreach ($fields as $key => $args) {
                            $args['key']       = $key;
                            $args['value']     = isset($editor_data[$key]) ? $editor_data[$key] : '';
                            $args['post_id']   = $post->ID;
                            echo PPS_Post_List_Box_Admin_UI::get_rendered_post_list_box_editor_partial($args);
                        }

                        wp_nonce_field('post-list-box-editor', 'post-list-box-editor-nonce');
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
        <div id="post-list-field-icons-modal" style="display: none;">
            <div id="post-list-field-icons-container" class="post-list-field-icons-container"></div>
        </div>
        <?php
    }

    /**
     * Get a rendered field partial
     *
     * @param array $args Arguments to render in the partial.
     */
    public static function get_rendered_post_list_box_editor_partial($args)
    {
        $defaults = [
            'type'        => 'text',
            'tab'         => PPS_Post_List_Box_Fields::default_tab(),
            'options'     => [],
            'value'       => '',
            'label'       => '',
            'group_title' => '',
            'description' => '',
            'min'         => '',
            'max'         => '',
            'placeholder' => '',
            'rows'        => '20',
            'readonly'    => false,
            'tabbed'      => 0,
            'tab_name'    => '',
            'show_input'  => false,
            'post_id'     => false,
            'group_start' => false,
            'group_end'   => false,
            'section'     => '',
            'collapsible' => false,
            'collapsed'   => false,
            'depends_on'  => '',
            'depends_value' => '',
        ];

        $args      = array_merge($defaults, $args);
        $key       = $args['key'];
        $tab_class = 'pps-boxes-editor-tab-content pps-' . $args['tab'] . '-tab ' . $args['type'] . ' pps-editor-' . $key;

        if ('range' === $args['type'] && $args['show_input']) {
            $tab_class .= ' double-input';
        }

        if ((int)$args['tabbed'] > 0) {
            $tab_class .= ' tabbed-content tabbed-content-' . $args['tab_name'];
        }

        // Add section class if specified
        if (!empty($args['section'])) {
            $tab_class .= ' pps-section-' . $args['section'];
        }

        // Add dependency attributes
        $dependency_attrs = '';
        if (!empty($args['depends_on'])) {
            $dependency_attrs = 'data-depends-on="' . esc_attr($args['depends_on']) . '" data-depends-value="' . esc_attr($args['depends_value']) . '"';
            $tab_class .= ' pps-conditional-field';
        }

        $tab_style = ($args['tab'] === PPS_Post_List_Box_Fields::default_tab()) ? '' : 'display:none;';
        ob_start();
        $generate_tab_title = false;

        // Handle section headers
        if ($args['type'] === 'section_header') {
            $collapsed_class = $args['collapsed'] ? 'collapsed' : '';
            $section_id = 'section-' . $args['section'];
            ?>
            <tr class="<?php echo esc_attr($tab_class); ?> pps-section-header" data-tab="<?php echo esc_attr($args['tab']); ?>" style="<?php echo esc_attr($tab_style); ?>">
                <td colspan="2" class="pps-section-header-cell">
                    <div class="pps-section-header-wrapper <?php echo esc_attr($collapsed_class); ?>" data-section="<?php echo esc_attr($args['section']); ?>">
                        <h4 class="pps-section-title">
                            <?php if ($args['collapsible']) : ?>
                                <span class="pps-section-toggle dashicons dashicons-arrow-down"></span>
                            <?php endif; ?>
                            <?php echo esc_html($args['label']); ?>
                        </h4>
                    </div>
                </td>
            </tr>
            <?php
            return ob_get_clean();
        }

        if (in_array($args['type'], ['textarea', 'export_action', 'import_action', 'template_action', 'line_break', 'code_editor'])) {
            $th_style = 'display: none;';
            $colspan  = 2;
        } else {
            $th_style = '';
            $colspan  = '';
        }
        ?>
        <?php if ($args['group_start'] === true) :
           ?>
            <tr
                class="group-title-row <?php echo esc_attr($tab_class); ?>"
                data-tab="<?php echo esc_attr($args['tab']); ?>"
                style="<?php echo esc_attr($tab_style); ?>"
            >
                <td colspan="2" style="padding-left: 0; padding-right: 0;">
                <div class="post-list-boxes-group-table-wrap">
                    <div class="table-title"><?php echo esc_html($args['group_title']); ?></div>
                        <table>
        <?php endif; ?>
        <tr
            class="<?php echo esc_attr($tab_class); ?>"
            data-tab="<?php echo esc_attr($args['tab']); ?>"
            style="<?php echo esc_attr($tab_style); ?>"
            <?php echo $dependency_attrs; ?>
            >
            <?php if (!empty($args['label'])) : ?>
                <th scope="row" style="<?php echo esc_attr($th_style); ?>">
                    <label for="<?php echo esc_attr($key); ?>">
                        <?php echo esc_html($args['label']); ?>
                    </label>
                </th>
            <?php endif; ?>
            <td class="input" colspan="<?php echo esc_attr($colspan); ?>">
                <?php
                if ('number' === $args['type']) :
                    ?>
                    <input name="<?php echo esc_attr($key); ?>"
                        id="<?php echo esc_attr($key); ?>"
                        type="<?php echo esc_attr($args['type']); ?>"
                        value="<?php echo esc_attr($args['value']); ?>"
                        min="<?php echo esc_attr($args['min']); ?>"
                        max="<?php echo esc_attr($args['max']); ?>"
                        placeholder="<?php echo esc_attr($args['placeholder']); ?>"
                        <?php echo (isset($args['readonly']) && $args['readonly'] === true) ? 'readonly' : ''; ?>
                         />
                        <?php
                elseif ('checkbox' === $args['type']) :
                    ?>
                    <input name="<?php echo esc_attr($key); ?>"
                        id="<?php echo esc_attr($key); ?>"
                        type="<?php echo esc_attr($args['type']); ?>"
                        value="1"
                        <?php echo (isset($args['readonly']) && $args['readonly'] === true) ? 'readonly' : ''; ?>
                        <?php checked($args['value'], 1); ?> />
                <?php
                elseif ('select' === $args['type']) :
                    ?>
                    <select name="<?php echo esc_attr($key); ?>"
                        id="<?php echo esc_attr($key); ?>"
                        placeholder="<?php echo esc_attr($args['placeholder']); ?>"
                        <?php echo (isset($args['readonly']) && $args['readonly'] === true) ? 'readonly' : ''; ?>
                        />
                        <?php foreach ($args['options'] as $key => $label) : ?>
                            <option value="<?php echo esc_attr($key); ?>"
                                <?php selected($key, $args['value']); ?>>
                                <?php echo esc_html($label); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                <?php
                elseif ('color' === $args['type']) :
                    ?>
                    <input name="<?php echo esc_attr($key); ?>"
                        class="pps-editor-color-picker"
                        id="<?php echo esc_attr($key); ?>"
                        type="text"
                        value="<?php echo esc_attr($args['value']); ?>" />
                <?php
                elseif ('textarea' === $args['type']) :
                    ?>
                    <textarea name="<?php echo esc_attr($key); ?>"
                        id="<?php echo esc_attr($key); ?>"
                        type="<?php echo esc_attr($args['type']); ?>"
                        rows="<?php echo esc_attr($args['rows']); ?>"
                        placeholder="<?php echo esc_attr($args['placeholder']); ?>"
                        <?php echo (isset($args['readonly']) && $args['readonly'] === true) ? 'readonly' : ''; ?>
                        ><?php echo esc_html($args['value']); ?></textarea>
                <?php
                elseif ('code_editor' === $args['type']) :
                    ?>
                    <label for="<?php echo esc_attr($key); ?>" class="code-editor-label">
                        <?php echo esc_html($args['label']); ?>
                    </label>
                    <div class="code-mirror-before"><div><?php echo htmlentities('<style type="text/css">'); ?></div></div>
                    <textarea
                        name="<?php echo esc_attr($key); ?>"
                        id="<?php echo esc_attr($key); ?>"
                        type="<?php echo esc_attr($args['type']); ?>"
                        rows="<?php echo esc_attr($args['rows']); ?>"
                        placeholder="<?php echo esc_attr($args['placeholder']); ?>"
                        data-editor_mode="<?php echo esc_attr($args['editor_mode']); ?>"
                        class="pps-post-list-code-editor"
                        <?php echo (isset($args['readonly']) && $args['readonly'] === true) ? 'readonly' : ''; ?>><?php echo esc_html($args['value']); ?></textarea>
                    <div class="code-mirror-after"><div><?php echo htmlentities('</style>'); ?></div></div>
                    <?php
                else : ?>
                    <input name="<?php echo esc_attr($key); ?>"
                        id="<?php echo esc_attr($key); ?>"
                        type="<?php echo esc_attr($args['type']); ?>"
                        value="<?php echo esc_attr($args['value']); ?>"
                        placeholder="<?php echo esc_attr($args['placeholder']); ?>"
                        <?php echo (isset($args['readonly']) && $args['readonly'] === true) ? 'readonly' : ''; ?>
                         />
                <?php endif; ?>
                <?php if (isset($args['description']) && !empty($args['description'])) : ?>
                        <?php if($args['type'] !== 'checkbox') : ?>
                            <br />
                        <?php endif; ?>
                        <span class="field-description description">
                            <?php echo $args['description']; ?>
                        </span>
                <?php endif; ?>
            </td>
        </tr>
        <?php if ($args['group_end'] === true) : ?>
                        </table>
                    </div>
                </td>
            </tr>
        <?php endif; ?>
        <?php
        return ob_get_clean();
    }
}