<?php
/**
 * Post List Box Fields class
 * Defines the fields for Post List Box editor
 */

class PostListBoxFields
{
    /**
     * Initialize the fields
     */
    public static function init()
    {
        add_filter('pps_post_list_box_editor_fields', [__CLASS__, 'get_fields'], 10, 2);
    }

    /**
     * Get all fields for the post list box editor
     *
     * @param array $fields Existing fields
     * @param WP_Post $post Post object
     * @return array
     */
    public static function get_fields($fields, $post)
    {
        // Ensure $fields is an array
        if (!is_array($fields)) {
            $fields = [];
        }

        // Title Section
        $fields = array_merge($fields, self::get_title_fields());

        // Layout Section
        $fields = array_merge($fields, self::get_layout_fields());

        // Item Section
        $fields = array_merge($fields, self::get_item_fields());

        

        return $fields;
    }

    /**
     * Get title fields
     *
     * @return array
     */
    private static function get_title_fields()
    {
        return [
            // Title Section Header
            'title_section_header' => [
                'label' => __('Title Settings', 'publishpress-series-pro'),
                'type' => 'section_header',
                'tab' => 'box',
                'section' => 'title',
                'collapsible' => true,
                'collapsed' => false,
            ],
            'title_show' => [
                'label' => __('Show Box Title', 'publishpress-series-pro'),
                'type' => 'checkbox',
                'tab' => 'box',
                'section' => 'title',
                'sanitize' => 'sanitize_text_field',
                'default' => 1,
            ],
            'title_type' => [
                'label' => __('Title Type', 'publishpress-series-pro'),
                'type' => 'select',
                'tab' => 'box',
                'section' => 'title',
                'options' => [
                    'series' => __('Use Series Title', 'publishpress-series-pro'),
                    'custom' => __('Custom Title', 'publishpress-series-pro'),
                ],
                'sanitize' => 'sanitize_text_field',
                'default' => 'series',
                'description' => __('Choose whether to display the series title or a custom title', 'publishpress-series-pro'),
                'depends_on' => 'title_show',
                'depends_value' => '1',
            ],
            'title_text' => [
                'label' => __('Custom Title Text', 'publishpress-series-pro'),
                'type' => 'text',
                'tab' => 'box',
                'section' => 'title',
                'sanitize' => 'sanitize_text_field',
                'default' => __('Series Posts', 'publishpress-series-pro'),
                'placeholder' => __('Enter custom title for the post list', 'publishpress-series-pro'),
                'description' => __('Only used when "Custom Title" is selected above', 'publishpress-series-pro'),
                'depends_on' => 'title_type',
                'depends_value' => 'custom',
            ],
            'title_html_tag' => [
                'label' => __('Title HTML Tag', 'publishpress-series-pro'),
                'type' => 'select',
                'tab' => 'box',
                'section' => 'title',
                'options' => [
                    'h1' => 'H1',
                    'h2' => 'H2',
                    'h3' => 'H3',
                    'h4' => 'H4',
                    'h5' => 'H5',
                    'h6' => 'H6',
                    'div' => 'DIV',
                    'p' => 'P',
                ],
                'sanitize' => 'sanitize_text_field',
                'default' => 'h3',
                'depends_on' => 'title_show',
                'depends_value' => '1',
            ],
            'title_color' => [
                'label' => __('Title Color', 'publishpress-series-pro'),
                'type' => 'color',
                'tab' => 'box',
                'section' => 'title',
                'sanitize' => 'sanitize_hex_color',
                'default' => '#333333',
                'depends_on' => 'title_show',
                'depends_value' => '1',
            ],
            'title_font_size' => [
                'label' => __('Title Font Size (px)', 'publishpress-series-pro'),
                'type' => 'number',
                'tab' => 'box',
                'section' => 'title',
                'min' => 10,
                'max' => 72,
                'sanitize' => 'absint',
                'default' => 24,
                'depends_on' => 'title_show',
                'depends_value' => '1',
            ],

            // Container Styling Section Header
            'container_section_header' => [
                'label' => __('Container Styling', 'publishpress-series-pro'),
                'type' => 'section_header',
                'tab' => 'box',
                'section' => 'container',
                'collapsible' => true,
                'collapsed' => true,
            ],
            'background_color' => [
                'label' => __('Background Color', 'publishpress-series-pro'),
                'type' => 'color',
                'tab' => 'box',
                'section' => 'container',
                'sanitize' => 'sanitize_hex_color',
                'default' => '#f9f9f9',
            ],
            'border_color' => [
                'label' => __('Border Color', 'publishpress-series-pro'),
                'type' => 'color',
                'tab' => 'box',
                'section' => 'container',
                'sanitize' => 'sanitize_hex_color',
                'default' => '#e5e5e5',
            ],
            'border_width' => [
                'label' => __('Border Width (px)', 'publishpress-series-pro'),
                'type' => 'number',
                'tab' => 'box',
                'section' => 'container',
                'min' => 0,
                'max' => 20,
                'sanitize' => 'absint',
                'default' => 1,
            ],
            'border_radius' => [
                'label' => __('Border Radius (px)', 'publishpress-series-pro'),
                'type' => 'number',
                'tab' => 'box',
                'section' => 'container',
                'min' => 0,
                'max' => 50,
                'sanitize' => 'absint',
                'default' => 4,
            ],
            'padding' => [
                'label' => __('Padding (px)', 'publishpress-series-pro'),
                'type' => 'number',
                'tab' => 'box',
                'section' => 'container',
                'min' => 0,
                'max' => 100,
                'sanitize' => 'absint',
                'default' => 20,
            ],
        ];
    }

    /**
     * Get item fields
     *
     * @return array
     */
    private static function get_item_fields()
    {
        return [
            // Post Content Section Header
            'content_section_header' => [
                'label' => __('Post Content', 'publishpress-series-pro'),
                'type' => 'section_header',
                'tab' => 'item',
                'section' => 'content',
                'collapsible' => true,
                'collapsed' => false,
            ],
            'show_post_titles' => [
                'label' => __('Show Post Titles', 'publishpress-series-pro'),
                'type' => 'checkbox',
                'tab' => 'item',
                'section' => 'content',
                'sanitize' => 'sanitize_text_field',
                'default' => 1,
            ],
            'post_title_color' => [
                'label' => __('Post Title Color', 'publishpress-series-pro'),
                'type' => 'color',
                'tab' => 'item',
                'section' => 'content',
                'sanitize' => 'sanitize_hex_color',
                'default' => '#0073aa',
                'depends_on' => 'show_post_titles',
                'depends_value' => '1',
            ],
            'post_title_font_size' => [
                'label' => __('Post Title Font Size (px)', 'publishpress-series-pro'),
                'type' => 'number',
                'tab' => 'item',
                'section' => 'content',
                'min' => 10,
                'max' => 36,
                'sanitize' => 'absint',
                'default' => 16,
                'depends_on' => 'show_post_titles',
                'depends_value' => '1',
            ],
            'show_post_excerpt' => [
                'label' => __('Show Post Excerpt', 'publishpress-series-pro'),
                'type' => 'checkbox',
                'tab' => 'item',
                'section' => 'content',
                'sanitize' => 'sanitize_text_field',
                'default' => 0,
            ],
            'excerpt_length' => [
                'label' => __('Excerpt Length (words)', 'publishpress-series-pro'),
                'type' => 'number',
                'tab' => 'item',
                'section' => 'content',
                'min' => 10,
                'max' => 500,
                'sanitize' => 'absint',
                'default' => 55,
                'depends_on' => 'show_post_excerpt',
                'depends_value' => '1',
            ],
            'excerpt_color' => [
                'label' => __('Excerpt Color', 'publishpress-series-pro'),
                'type' => 'color',
                'tab' => 'item',
                'section' => 'content',
                'sanitize' => 'sanitize_hex_color',
                'default' => '#666666',
                'depends_on' => 'show_post_excerpt',
                'depends_value' => '1',
            ],

            // Thumbnail Section Header
            'thumbnail_section_header' => [
                'label' => __('Post Thumbnails', 'publishpress-series-pro'),
                'type' => 'section_header',
                'tab' => 'item',
                'section' => 'thumbnail',
                'collapsible' => true,
                'collapsed' => true,
            ],
            'show_post_thumbnail' => [
                'label' => __('Show Post Thumbnail', 'publishpress-series-pro'),
                'type' => 'checkbox',
                'tab' => 'item',
                'section' => 'thumbnail',
                'sanitize' => 'sanitize_text_field',
                'default' => 1,
            ],
            'thumbnail_width' => [
                'label' => __('Thumbnail Width (px)', 'publishpress-series-pro'),
                'type' => 'number',
                'tab' => 'item',
                'section' => 'thumbnail',
                'min' => 50,
                'max' => 800,
                'sanitize' => 'absint',
                'default' => 150,
                'description' => __('Width of the thumbnail image in pixels', 'publishpress-series-pro'),
                'depends_on' => 'show_post_thumbnail',
                'depends_value' => '1',
            ],
            'thumbnail_height' => [
                'label' => __('Thumbnail Height (px)', 'publishpress-series-pro'),
                'type' => 'number',
                'tab' => 'item',
                'section' => 'thumbnail',
                'min' => 50,
                'max' => 800,
                'sanitize' => 'absint',
                'default' => 150,
                'description' => __('Height of the thumbnail image in pixels', 'publishpress-series-pro'),
                'depends_on' => 'show_post_thumbnail',
                'depends_value' => '1',
            ],

            // Meta Information Section Header
            'meta_section_header' => [
                'label' => __('Meta Information', 'publishpress-series-pro'),
                'type' => 'section_header',
                'tab' => 'item',
                'section' => 'meta',
                'collapsible' => true,
                'collapsed' => true,
            ],
            'show_post_author' => [
                'label' => __('Show Post Author', 'publishpress-series-pro'),
                'type' => 'checkbox',
                'tab' => 'item',
                'section' => 'meta',
                'sanitize' => 'sanitize_text_field',
                'default' => 0,
            ],
            'show_post_date' => [
                'label' => __('Show Post Date', 'publishpress-series-pro'),
                'type' => 'checkbox',
                'tab' => 'item',
                'section' => 'meta',
                'sanitize' => 'sanitize_text_field',
                'default' => 1,
            ],

            // Item Styling Section Header
            'item_styling_section_header' => [
                'label' => __('Item Styling', 'publishpress-series-pro'),
                'type' => 'section_header',
                'tab' => 'item',
                'section' => 'item_styling',
                'collapsible' => true,
                'collapsed' => true,
            ],
            'item_padding' => [
                'label' => __('Item Padding (px)', 'publishpress-series-pro'),
                'type' => 'number',
                'tab' => 'item',
                'section' => 'item_styling',
                'min' => 0,
                'max' => 100,
                'sanitize' => 'absint',
                'default' => 15,
            ],
            'item_border_width' => [
                'label' => __('Item Border Width (px)', 'publishpress-series-pro'),
                'type' => 'number',
                'tab' => 'item',
                'section' => 'item_styling',
                'min' => 0,
                'max' => 20,
                'sanitize' => 'absint',
                'default' => 1,
            ],
            'item_border_color' => [
                'label' => __('Item Border Color', 'publishpress-series-pro'),
                'type' => 'color',
                'tab' => 'item',
                'section' => 'item_styling',
                'sanitize' => 'sanitize_hex_color',
                'default' => '#e5e5e5',
            ],
            'post_list_background_color' => [
                'label' => __('Post List Background Color', 'publishpress-series-pro'),
                'type' => 'color',
                'tab' => 'item',
                'section' => 'item_styling',
                'sanitize' => 'sanitize_hex_color',
                'default' => '#ffffff',
                'description' => __('Background color for the post list container', 'publishpress-series-pro'),
            ],
        ];
    }

    /**
     * Get layout fields
     *
     * @return array
     */
    private static function get_layout_fields()
    {
        return [
            // Layout Structure Section Header
            'layout_structure_section_header' => [
                'label' => __('Layout Structure', 'publishpress-series-pro'),
                'type' => 'section_header',
                'tab' => 'layout',
                'section' => 'structure',
                'collapsible' => true,
                'collapsed' => false,
            ],
            'layout_style' => [
                'label' => __('Layout Style', 'publishpress-series-pro'),
                'type' => 'select',
                'tab' => 'layout',
                'section' => 'structure',
                'options' => [
                    'list' => __('List', 'publishpress-series-pro'),
                    'grid' => __('Grid', 'publishpress-series-pro'),
                ],
                'sanitize' => 'sanitize_text_field',
                'default' => 'list',
            ],
            'columns' => [
                'label' => __('Columns', 'publishpress-series-pro'),
                'type' => 'select',
                'tab' => 'layout',
                'section' => 'structure',
                'options' => [
                    '2' => __('2 Columns', 'publishpress-series-pro'),
                    '3' => __('3 Columns', 'publishpress-series-pro'),
                    '4' => __('4 Columns', 'publishpress-series-pro'),
                    '6' => __('6 Columns', 'publishpress-series-pro'),
                ],
                'sanitize' => 'absint',
                'default' => '3',
                'depends_on' => 'layout_style',
                'depends_value' => 'grid',
            ],
            'gap_between_items' => [
                'label' => __('Gap Between Items (px)', 'publishpress-series-pro'),
                'type' => 'number',
                'tab' => 'layout',
                'section' => 'structure',
                'min' => 0,
                'max' => 50,
                'sanitize' => 'absint',
                'default' => 10,
            ],

            // Post Ordering Section Header
            'ordering_section_header' => [
                'label' => __('Post Ordering', 'publishpress-series-pro'),
                'type' => 'section_header',
                'tab' => 'layout',
                'section' => 'ordering',
                'collapsible' => true,
                'collapsed' => true,
            ],
            'orderby' => [
                'label' => __('Order By', 'publishpress-series-pro'),
                'type' => 'select',
                'tab' => 'layout',
                'section' => 'ordering',
                'options' => [
                    'date' => __('Date', 'publishpress-series-pro'),
                    'title' => __('Title', 'publishpress-series-pro'),
                    'series_order' => __('Series Order', 'publishpress-series-pro'),
                ],
                'sanitize' => 'sanitize_text_field',
                'default' => 'series_order',
            ],
            'order' => [
                'label' => __('Order', 'publishpress-series-pro'),
                'type' => 'select',
                'tab' => 'layout',
                'section' => 'ordering',
                'options' => [
                    'ASC' => __('Ascending', 'publishpress-series-pro'),
                    'DESC' => __('Descending', 'publishpress-series-pro'),
                ],
                'sanitize' => 'sanitize_text_field',
                'default' => 'ASC',
            ],

            // Current Post Highlighting Section Header
            'highlighting_section_header' => [
                'label' => __('Current Post Highlighting', 'publishpress-series-pro'),
                'type' => 'section_header',
                'tab' => 'layout',
                'section' => 'highlighting',
                'collapsible' => true,
                'collapsed' => true,
            ],
            'highlight_current_post' => [
                'label' => __('Highlight Current Post', 'publishpress-series-pro'),
                'type' => 'checkbox',
                'tab' => 'layout',
                'section' => 'highlighting',
                'sanitize' => 'sanitize_text_field',
                'default' => 1,
                'description' => __('Highlight the current post when viewing a specific series post', 'publishpress-series-pro'),
            ],
            'current_post_bg_color' => [
                'label' => __('Current Post Background Color', 'publishpress-series-pro'),
                'type' => 'color',
                'tab' => 'layout',
                'section' => 'highlighting',
                'sanitize' => 'sanitize_hex_color',
                'default' => '#fff3cd',
                'depends_on' => 'highlight_current_post',
                'depends_value' => '1',
            ],
            'current_post_border_color' => [
                'label' => __('Current Post Border Color', 'publishpress-series-pro'),
                'type' => 'color',
                'tab' => 'layout',
                'section' => 'highlighting',
                'sanitize' => 'sanitize_hex_color',
                'default' => '#ffeaa7',
                'depends_on' => 'highlight_current_post',
                'depends_value' => '1',
            ],
            'current_post_text_color' => [
                'label' => __('Current Post Text Color', 'publishpress-series-pro'),
                'type' => 'color',
                'tab' => 'layout',
                'section' => 'highlighting',
                'sanitize' => 'sanitize_hex_color',
                'default' => '#856404',
                'depends_on' => 'highlight_current_post',
                'depends_value' => '1',
            ],
        ];
    }

    /**
     * Initialize the class
     */
    public static function register()
    {
        static::init();
    }
}

// Initialize the fields
PostListBoxFields::register();